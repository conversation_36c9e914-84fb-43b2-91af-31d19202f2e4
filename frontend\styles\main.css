/* 全局样式重置 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #0a1428 0%, #1e3a5f 50%, #0a1428 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}


/* 主容器 */

.dashboard-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: radial-gradient(circle at 20% 20%, rgba(0, 150, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(0, 255, 150, 0.1) 0%, transparent 50%), linear-gradient(135deg, #0a1428 0%, #1e3a5f 50%, #0a1428 100%);
}


/* 顶部导航栏 */

.header {
    height: 80px;
    background: linear-gradient(90deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 100, 200, 0.6) 50%, rgba(0, 50, 100, 0.8) 100%);
    border-bottom: 2px solid rgba(0, 150, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    backdrop-filter: blur(10px);
}

.header-left .title {
    font-size: 28px;
    font-weight: bold;
    color: #00d4ff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    margin-bottom: 5px;
}

.header-left .subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-item {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.nav-item:hover,
.nav-item.active {
    background: linear-gradient(45deg, rgba(0, 150, 255, 0.3), rgba(0, 255, 150, 0.3));
    color: #ffffff;
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.4);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.time-display {
    font-size: 18px;
    color: #00d4ff;
    font-weight: bold;
}

.weather-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
}


/* 主内容区域 */

.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    height: calc(100vh - 80px);
}


/* 侧边面板通用样式 */

.left-panel,
.right-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.panel-section {
    background: linear-gradient(145deg, rgba(0, 50, 100, 0.4), rgba(0, 100, 150, 0.2));
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 12px;
    padding: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.section-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 150, 255, 0.3);
}

.section-header h3 {
    color: #00d4ff;
    font-size: 16px;
    font-weight: bold;
}


/* 视频容器 */

.video-container {
    height: 180px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.video-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1a2332, #2a3442);
    position: relative;
    border: 1px solid rgba(0, 150, 255, 0.2);
}

.video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

.camera-icon {
    font-size: 40px;
    margin-bottom: 10px;
}

.video-info div {
    font-size: 12px;
    margin: 2px 0;
}


/* 图表容器 */

.chart-container {
    height: 200px;
}

.chart {
    width: 100%;
    height: 100%;
}


/* 速度统计 */

.speed-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.speed-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(0, 50, 100, 0.3);
    border-radius: 8px;
    border-left: 3px solid #00d4ff;
}

.speed-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.speed-value {
    color: #00ff88;
    font-size: 18px;
    font-weight: bold;
}

.speed-value span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.speed-value.congestion {
    color: #ff6b6b;
}


/* 中央场景区域 */

.center-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.scene-container {
    flex: 1;
    background: linear-gradient(145deg, rgba(0, 50, 100, 0.4), rgba(0, 100, 150, 0.2));
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.road-scene {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 40%, rgba(0, 150, 255, 0.1) 50%, transparent 60%), linear-gradient(-45deg, transparent 40%, rgba(0, 255, 150, 0.1) 50%, transparent 60%), radial-gradient(circle at center, rgba(0, 100, 200, 0.2) 0%, transparent 70%), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,150,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    position: relative;
}

.scene-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}


/* 车辆和事件标记 */

.vehicle-marker,
.incident-marker {
    position: absolute;
    pointer-events: auto;
    cursor: pointer;
}

.marker-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #00ff88;
    border: 2px solid #ffffff;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
    animation: pulse 2s infinite;
}

.marker-dot.incident {
    background: #ff4757;
    box-shadow: 0 0 15px rgba(255, 71, 87, 0.6);
}

.marker-info {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 5px 10px;
    border-radius: 6px;
    font-size: 11px;
    white-space: nowrap;
    border: 1px solid rgba(0, 150, 255, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.vehicle-marker:hover .marker-info,
.incident-marker:hover .marker-info {
    opacity: 1;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}


/* 控制面板 */

.control-panel {
    height: 80px;
    background: linear-gradient(145deg, rgba(0, 50, 100, 0.4), rgba(0, 100, 150, 0.2));
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 12px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.control-btn {
    background: linear-gradient(45deg, #0066cc, #0099ff);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 150, 255, 0.3);
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 150, 255, 0.4);
}

.status-indicators {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: blink 2s infinite;
}

.status-dot.green {
    background: #00ff88;
}

.status-dot.yellow {
    background: #ffd700;
}

.status-dot.red {
    background: #ff4757;
}

@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0.3;
    }
}


/* 实时数据 */

.realtime-data {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 150, 255, 0.1);
}

.data-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
}

.data-value {
    color: #00ff88;
    font-weight: bold;
    font-size: 14px;
}

.data-value.warning {
    color: #ff6b6b;
}

.data-value small {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: normal;
}


/* 提醒容器 */

.alert-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.alert {
    min-width: 300px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease;
}

.alert-success {
    border-left-color: #00ff88;
}

.alert-warning {
    border-left-color: #ffd700;
}

.alert-error {
    border-left-color: #ff4757;
}

.alert-info {
    border-left-color: #00d4ff;
}

.alert-content {
    display: flex;
    align-items: center;
    padding: 15px;
    gap: 10px;
}

.alert-icon {
    font-size: 18px;
    font-weight: bold;
}

.alert-success .alert-icon {
    color: #00ff88;
}

.alert-warning .alert-icon {
    color: #ffd700;
}

.alert-error .alert-icon {
    color: #ff4757;
}

.alert-info .alert-icon {
    color: #00d4ff;
}

.alert-message {
    flex: 1;
    color: #ffffff;
    font-size: 14px;
}

.alert-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.alert-close:hover {
    color: #ffffff;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}


/* 响应式设计 */

@media (max-width: 1400px) {
    .main-content {
        grid-template-columns: 280px 1fr 280px;
    }
}

@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 250px 1fr 250px;
    }
    .header-left .title {
        font-size: 24px;
    }
    .alert-container {
        right: 10px;
    }
    .alert {
        min-width: 250px;
    }
}