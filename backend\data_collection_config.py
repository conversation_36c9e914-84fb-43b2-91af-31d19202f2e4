# -*- coding: utf-8 -*-
"""
数据采集配置文件
定义各种数据源的配置参数
"""

# 地磁线圈检测器配置
MAGNETIC_LOOP_CONFIG = {
    'source_id': 'magnetic_loop_001',
    'positions': [
        {
            'id': 'ML001',
            'name': 'G15沈海高速K1234+500东向',
            'coordinates': {'lat': 31.2304, 'lng': 121.4737},
            'lane_number': 1,
            'direction': '东向西'
        },
        {
            'id': 'ML002', 
            'name': 'G15沈海高速K1234+500西向',
            'coordinates': {'lat': 31.2304, 'lng': 121.4737},
            'lane_number': 2,
            'direction': '西向东'
        },
        {
            'id': 'ML003',
            'name': 'G15沈海高速K1235+200南向',
            'coordinates': {'lat': 31.2404, 'lng': 121.4837},
            'lane_number': 1,
            'direction': '南向北'
        },
        {
            'id': 'ML004',
            'name': 'G15沈海高速K1235+200北向',
            'coordinates': {'lat': 31.2404, 'lng': 121.4837},
            'lane_number': 2,
            'direction': '北向南'
        }
    ],
    'sensitivity': 0.8,
    'sampling_rate': 100,  # Hz
    'detection_threshold': 0.3
}

# 摄像头检测器配置
CAMERA_CONFIG = {
    'source_id': 'camera_system_001',
    'camera_urls': [
        {
            'id': 'CAM001',
            'name': '主要路口01摄像头',
            'url': 'rtsp://192.168.1.100:554/stream1',
            'location': '主要路口01',
            'coordinates': {'lat': 31.2204, 'lng': 121.4637},
            'resolution': '1920x1080',
            'fps': 25,
            'detection_zones': [
                {'name': '东西向车道', 'polygon': [[100, 200], [800, 200], [800, 400], [100, 400]]},
                {'name': '南北向车道', 'polygon': [[400, 50], [600, 50], [600, 600], [400, 600]]}
            ]
        },
        {
            'id': 'CAM002',
            'name': '主要路口02摄像头',
            'url': 'rtsp://192.168.1.101:554/stream1',
            'location': '主要路口02',
            'coordinates': {'lat': 31.2504, 'lng': 121.4937},
            'resolution': '1920x1080',
            'fps': 25,
            'detection_zones': [
                {'name': '主车道', 'polygon': [[0, 300], [1920, 300], [1920, 700], [0, 700]]},
                {'name': '辅助车道', 'polygon': [[0, 700], [1920, 700], [1920, 900], [0, 900]]}
            ]
        },
        {
            'id': 'CAM003',
            'name': '高速路段监控',
            'url': 'rtsp://192.168.1.102:554/stream1',
            'location': 'G15沈海高速K1234段',
            'coordinates': {'lat': 31.2354, 'lng': 121.4787},
            'resolution': '2560x1440',
            'fps': 30,
            'detection_zones': [
                {'name': '快车道', 'polygon': [[0, 200], [2560, 200], [2560, 600], [0, 600]]},
                {'name': '慢车道', 'polygon': [[0, 600], [2560, 600], [2560, 1000], [0, 1000]]}
            ]
        }
    ],
    'ai_model_config': {
        'vehicle_detection_model': 'yolov5s',
        'confidence_threshold': 0.5,
        'nms_threshold': 0.4,
        'vehicle_classes': ['car', 'truck', 'bus', 'motorcycle'],
        'tracking_enabled': True,
        'speed_estimation_enabled': True
    }
}

# 手机信令检测器配置
MOBILE_SIGNAL_CONFIG = {
    'source_id': 'mobile_signal_001',
    'base_stations': [
        {
            'id': 'BS001',
            'name': '基站001-市中心',
            'location': '市中心商业区',
            'coordinates': {'lat': 31.2304, 'lng': 121.4737},
            'coverage_radius': 1500,  # 覆盖半径（米）
            'frequency_bands': ['2G', '3G', '4G', '5G'],
            'operator': 'China Mobile'
        },
        {
            'id': 'BS002',
            'name': '基站002-高速路段',
            'location': 'G15沈海高速沿线',
            'coordinates': {'lat': 31.2404, 'lng': 121.4837},
            'coverage_radius': 2000,
            'frequency_bands': ['4G', '5G'],
            'operator': 'China Telecom'
        },
        {
            'id': 'BS003',
            'name': '基站003-住宅区',
            'location': '城东住宅区',
            'coordinates': {'lat': 31.2504, 'lng': 121.4937},
            'coverage_radius': 1200,
            'frequency_bands': ['3G', '4G', '5G'],
            'operator': 'China Unicom'
        },
        {
            'id': 'BS004',
            'name': '基站004-工业园区',
            'location': '城西工业园区',
            'coordinates': {'lat': 31.2104, 'lng': 121.4537},
            'coverage_radius': 1800,
            'frequency_bands': ['4G', '5G'],
            'operator': 'China Mobile'
        }
    ],
    'signal_threshold': -80,  # dBm
    'anonymization_enabled': True,
    'privacy_protection': {
        'device_id_hashing': True,
        'location_fuzzing': True,
        'data_retention_days': 7
    }
}

# 其他传感器配置
OTHER_SENSORS_CONFIG = {
    # 雷达检测器
    'radar_detectors': [
        {
            'id': 'RD001',
            'name': '毫米波雷达001',
            'location': '主要路口01',
            'coordinates': {'lat': 31.2204, 'lng': 121.4637},
            'frequency': '77GHz',
            'detection_range': 200,  # 米
            'angle_coverage': 120,   # 度
            'speed_accuracy': 0.1    # km/h
        }
    ],
    
    # 红外检测器
    'infrared_detectors': [
        {
            'id': 'IR001',
            'name': '红外检测器001',
            'location': '收费站入口',
            'coordinates': {'lat': 31.2154, 'lng': 121.4687},
            'detection_distance': 50,  # 米
            'temperature_range': [-20, 60]  # 摄氏度
        }
    ],
    
    # 超声波检测器
    'ultrasonic_detectors': [
        {
            'id': 'US001',
            'name': '超声波检测器001',
            'location': '停车场入口',
            'coordinates': {'lat': 31.2254, 'lng': 121.4787},
            'frequency': '40kHz',
            'detection_range': 10  # 米
        }
    ]
}

# 数据质量配置
DATA_QUALITY_CONFIG = {
    'validation_rules': {
        'magnetic_loop': {
            'required_fields': ['position_id', 'detection_time', 'magnetic_strength'],
            'value_ranges': {
                'magnetic_strength': [0.0, 2.0],
                'vehicle_length': [1.0, 20.0],
                'estimated_speed': [0.0, 200.0]
            }
        },
        'camera': {
            'required_fields': ['camera_id', 'capture_time', 'detected_vehicles'],
            'value_ranges': {
                'confidence': [0.0, 1.0],
                'vehicle_count': [0, 50],
                'image_quality': [0.0, 1.0]
            }
        },
        'mobile_signal': {
            'required_fields': ['station_id', 'collection_time', 'connected_devices'],
            'value_ranges': {
                'signal_strength': [-120.0, -30.0],
                'connected_devices': [0, 10000],
                'coverage_radius': [100, 5000]
            }
        }
    },
    
    'data_fusion_rules': {
        'time_window': 300,  # 秒，数据融合时间窗口
        'location_radius': 500,  # 米，位置匹配半径
        'confidence_weights': {
            'magnetic_loop': 0.9,
            'camera': 0.8,
            'mobile_signal': 0.6,
            'radar': 0.85,
            'infrared': 0.7
        }
    },
    
    'anomaly_detection': {
        'enabled': True,
        'methods': ['statistical', 'isolation_forest', 'lstm'],
        'thresholds': {
            'flow_anomaly': 3.0,  # 标准差倍数
            'speed_anomaly': 2.5,
            'signal_anomaly': 2.0
        }
    }
}

# 数据存储配置
DATA_STORAGE_CONFIG = {
    'database': {
        'batch_size': 1000,
        'commit_interval': 30,  # 秒
        'retention_policy': {
            'raw_data': 30,      # 天
            'processed_data': 90,
            'aggregated_data': 365
        }
    },
    
    'file_storage': {
        'enabled': True,
        'formats': ['json', 'csv', 'parquet'],
        'compression': 'gzip',
        'backup_enabled': True,
        'backup_interval': 3600  # 秒
    },
    
    'real_time_streaming': {
        'enabled': True,
        'kafka_config': {
            'bootstrap_servers': ['localhost:9092'],
            'topics': {
                'raw_data': 'traffic_raw_data',
                'processed_data': 'traffic_processed_data',
                'alerts': 'traffic_alerts'
            }
        }
    }
}

# 采集调度配置
COLLECTION_SCHEDULE_CONFIG = {
    'magnetic_loop': {
        'interval': 1,      # 秒
        'enabled': True,
        'priority': 'high'
    },
    'camera': {
        'interval': 5,      # 秒
        'enabled': True,
        'priority': 'high'
    },
    'mobile_signal': {
        'interval': 60,     # 秒
        'enabled': True,
        'priority': 'medium'
    },
    'radar': {
        'interval': 2,      # 秒
        'enabled': False,   # 默认关闭，需要硬件支持
        'priority': 'high'
    },
    'infrared': {
        'interval': 10,     # 秒
        'enabled': False,
        'priority': 'low'
    }
}

# 系统监控配置
MONITORING_CONFIG = {
    'health_check': {
        'interval': 30,     # 秒
        'timeout': 10,      # 秒
        'retry_count': 3
    },
    
    'performance_metrics': {
        'collection_rate': True,
        'processing_latency': True,
        'error_rate': True,
        'data_quality_score': True
    },
    
    'alerts': {
        'enabled': True,
        'channels': ['email', 'sms', 'webhook'],
        'thresholds': {
            'data_loss_rate': 0.05,      # 5%
            'processing_delay': 300,      # 秒
            'error_rate': 0.1,           # 10%
            'device_offline_time': 600    # 秒
        }
    }
}
