// 主应用程序
class TrafficDashboard {
    constructor() {
        this.charts = null;
        this.scene = null;
        this.init();
    }

    // 初始化应用
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initComponents());
        } else {
            this.initComponents();
        }
    }

    // 初始化组件
    initComponents() {
        // 初始化时间显示
        this.initTimeDisplay();

        // 初始化图表
        this.charts = new TrafficCharts();

        // 初始化3D场景
        this.scene = new TrafficScene();

        // 初始化事件监听
        this.initEventListeners();

        // 初始化动画效果
        this.initAnimations();

        // 开始数据模拟
        this.startDataSimulation();

        console.log('智慧交通运营管理平台已启动');
    }

    // 初始化时间显示
    initTimeDisplay() {
        const timeElement = document.getElementById('currentTime');
        if (!timeElement) return;

        const updateTime = () => {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
            timeElement.textContent = `${dateString} ${timeString}`;
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    // 初始化事件监听
    initEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            if (this.charts) {
                this.charts.handleResize();
            }
            if (this.scene) {
                this.scene.handleResize();
            }
        });

        // 导航菜单点击
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavigation(item);
            });
        });

        // 控制按钮点击
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleControlAction(btn);
            });
        });

        // 车辆标记点击
        document.querySelectorAll('.vehicle-marker, .incident-marker').forEach(marker => {
            marker.addEventListener('click', () => {
                this.handleMarkerClick(marker);
            });
        });
    }

    // 处理导航
    handleNavigation(navItem) {
        // 移除所有活动状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加活动状态
        navItem.classList.add('active');

        // 这里可以添加页面切换逻辑
        const page = navItem.textContent.trim();
        console.log(`切换到页面: ${page}`);
    }

    // 处理控制操作
    handleControlAction(button) {
        const action = button.textContent.trim();
        
        // 添加点击效果
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);

        console.log(`执行操作: ${action}`);
        
        // 这里可以添加具体的控制逻辑
        if (action === '监测预警') {
            this.showAlert('监测预警系统已激活', 'success');
        }
    }

    // 处理标记点击
    handleMarkerClick(marker) {
        const info = marker.querySelector('.marker-info');
        if (info) {
            // 显示详细信息
            const content = info.textContent;
            this.showAlert(`详细信息: ${content}`, 'info');
        }
    }

    // 初始化动画效果
    initAnimations() {
        // 为面板添加进入动画
        const panels = document.querySelectorAll('.panel-section');
        panels.forEach((panel, index) => {
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                panel.style.transition = 'all 0.6s ease';
                panel.style.opacity = '1';
                panel.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // 添加数据更新动画
        this.addDataUpdateAnimations();
    }

    // 添加数据更新动画
    addDataUpdateAnimations() {
        const dataValues = document.querySelectorAll('.data-value, .speed-value');
        dataValues.forEach(element => {
            element.addEventListener('DOMSubtreeModified', () => {
                element.style.transform = 'scale(1.1)';
                element.style.color = '#00ff88';
                
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 300);
            });
        });
    }

    // 开始数据模拟
    startDataSimulation() {
        // 模拟车辆移动
        this.simulateVehicleMovement();

        // 模拟事件发生
        this.simulateEvents();
    }

    // 模拟车辆移动
    simulateVehicleMovement() {
        const markers = document.querySelectorAll('.vehicle-marker');
        
        setInterval(() => {
            markers.forEach(marker => {
                const currentLeft = parseFloat(marker.style.left) || 20;
                const currentTop = parseFloat(marker.style.top) || 40;
                
                // 随机移动
                const newLeft = Math.max(10, Math.min(90, currentLeft + (Math.random() - 0.5) * 10));
                const newTop = Math.max(20, Math.min(80, currentTop + (Math.random() - 0.5) * 10));
                
                marker.style.left = newLeft + '%';
                marker.style.top = newTop + '%';
            });
        }, 5000);
    }

    // 模拟事件发生
    simulateEvents() {
        setInterval(() => {
            if (Math.random() < 0.3) { // 30%概率发生事件
                this.generateRandomEvent();
            }
        }, 15000);
    }

    // 生成随机事件
    generateRandomEvent() {
        const events = [
            { type: 'warning', message: '检测到车辆超速', location: 'K1234+500' },
            { type: 'info', message: '车流量增加', location: 'K1235+200' },
            { type: 'error', message: '发现交通事故', location: 'K1236+100' },
            { type: 'success', message: '拥堵已缓解', location: 'K1237+300' }
        ];

        const event = events[Math.floor(Math.random() * events.length)];
        this.showAlert(`${event.message} - ${event.location}`, event.type);
    }

    // 显示提醒
    showAlert(message, type = 'info') {
        const alertContainer = this.getOrCreateAlertContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <div class="alert-content">
                <span class="alert-icon">${this.getAlertIcon(type)}</span>
                <span class="alert-message">${message}</span>
                <button class="alert-close">&times;</button>
            </div>
        `;

        alertContainer.appendChild(alert);

        // 添加关闭事件
        alert.querySelector('.alert-close').addEventListener('click', () => {
            alert.remove();
        });

        // 自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    // 获取或创建提醒容器
    getOrCreateAlertContainer() {
        let container = document.getElementById('alertContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'alertContainer';
            container.className = 'alert-container';
            document.body.appendChild(container);
        }
        return container;
    }

    // 获取提醒图标
    getAlertIcon(type) {
        const icons = {
            success: '✓',
            warning: '⚠',
            error: '✗',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }

    // 销毁应用
    destroy() {
        if (this.scene) {
            this.scene.destroy();
        }
        
        // 清理事件监听器
        window.removeEventListener('resize', this.handleResize);
    }
}

// 启动应用
const dashboard = new TrafficDashboard();

// 导出到全局
window.TrafficDashboard = TrafficDashboard;
