# -*- coding: utf-8 -*-
"""
数据库模型定义
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class TrafficFlow(db.Model):
    """车流量数据表"""
    __tablename__ = 'traffic_flow'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    location_id = db.Column(db.String(50), nullable=False, comment='位置ID')
    location_name = db.Column(db.String(100), nullable=False, comment='位置名称')
    flow_count = db.Column(db.Integer, nullable=False, comment='车流量')
    record_time = db.Column(db.DateTime, nullable=False, comment='记录时间')
    vehicle_type = db.Column(db.String(20), default='all', comment='车辆类型')
    direction = db.Column(db.String(20), comment='行驶方向')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'location_id': self.location_id,
            'location_name': self.location_name,
            'flow_count': self.flow_count,
            'record_time': self.record_time.isoformat() if self.record_time else None,
            'vehicle_type': self.vehicle_type,
            'direction': self.direction,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class VehicleSpeed(db.Model):
    """车辆速度数据表"""
    __tablename__ = 'vehicle_speed'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    vehicle_id = db.Column(db.String(50), comment='车辆ID')
    location_id = db.Column(db.String(50), nullable=False, comment='位置ID')
    speed = db.Column(db.Float, nullable=False, comment='车速(km/h)')
    speed_limit = db.Column(db.Float, default=60.0, comment='限速(km/h)')
    record_time = db.Column(db.DateTime, nullable=False, comment='记录时间')
    is_violation = db.Column(db.Boolean, default=False, comment='是否超速')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'vehicle_id': self.vehicle_id,
            'location_id': self.location_id,
            'speed': self.speed,
            'speed_limit': self.speed_limit,
            'record_time': self.record_time.isoformat() if self.record_time else None,
            'is_violation': self.is_violation,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class TrafficEvent(db.Model):
    """交通事件表"""
    __tablename__ = 'traffic_event'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    event_type = db.Column(db.String(20), nullable=False, comment='事件类型')
    event_level = db.Column(db.String(20), default='normal', comment='事件级别')
    location_id = db.Column(db.String(50), nullable=False, comment='位置ID')
    location_name = db.Column(db.String(100), nullable=False, comment='位置名称')
    description = db.Column(db.Text, comment='事件描述')
    status = db.Column(db.String(20), default='active', comment='事件状态')
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'event_type': self.event_type,
            'event_level': self.event_level,
            'location_id': self.location_id,
            'location_name': self.location_name,
            'description': self.description,
            'status': self.status,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class DeviceStatus(db.Model):
    """设备状态表"""
    __tablename__ = 'device_status'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    device_id = db.Column(db.String(50), nullable=False, unique=True, comment='设备ID')
    device_name = db.Column(db.String(100), nullable=False, comment='设备名称')
    device_type = db.Column(db.String(20), nullable=False, comment='设备类型')
    location_id = db.Column(db.String(50), nullable=False, comment='位置ID')
    status = db.Column(db.String(20), default='online', comment='设备状态')
    last_heartbeat = db.Column(db.DateTime, comment='最后心跳时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'device_id': self.device_id,
            'device_name': self.device_name,
            'device_type': self.device_type,
            'location_id': self.location_id,
            'status': self.status,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ViolationRecord(db.Model):
    """违法记录表"""
    __tablename__ = 'violation_record'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    vehicle_id = db.Column(db.String(50), comment='车辆ID')
    violation_type = db.Column(db.String(20), nullable=False, comment='违法类型')
    location_id = db.Column(db.String(50), nullable=False, comment='位置ID')
    location_name = db.Column(db.String(100), nullable=False, comment='位置名称')
    description = db.Column(db.Text, comment='违法描述')
    evidence_data = db.Column(db.Text, comment='证据数据(JSON)')
    status = db.Column(db.String(20), default='pending', comment='处理状态')
    record_time = db.Column(db.DateTime, nullable=False, comment='违法时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    def to_dict(self):
        evidence = None
        if self.evidence_data:
            try:
                evidence = json.loads(self.evidence_data)
            except:
                evidence = self.evidence_data
                
        return {
            'id': self.id,
            'vehicle_id': self.vehicle_id,
            'violation_type': self.violation_type,
            'location_id': self.location_id,
            'location_name': self.location_name,
            'description': self.description,
            'evidence_data': evidence,
            'status': self.status,
            'record_time': self.record_time.isoformat() if self.record_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class IntersectionStatus(db.Model):
    """路口状态表"""
    __tablename__ = 'intersection_status'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    intersection_id = db.Column(db.String(50), nullable=False, comment='路口ID')
    intersection_name = db.Column(db.String(100), nullable=False, comment='路口名称')
    traffic_light_phase = db.Column(db.String(10), default='red', comment='信号灯相位')
    phase_remaining_time = db.Column(db.Integer, default=30, comment='相位剩余时间')
    east_west_flow = db.Column(db.Integer, default=0, comment='东西向车流量')
    north_south_flow = db.Column(db.Integer, default=0, comment='南北向车流量')
    average_speed = db.Column(db.Float, default=0.0, comment='平均车速')
    incident_count = db.Column(db.Integer, default=0, comment='事件数量')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'intersection_id': self.intersection_id,
            'intersection_name': self.intersection_name,
            'traffic_light_status': {
                'current_phase': self.traffic_light_phase,
                'remaining_time': self.phase_remaining_time
            },
            'vehicle_count': {
                'east_west': self.east_west_flow,
                'north_south': self.north_south_flow
            },
            'average_speed': self.average_speed,
            'incident_count': self.incident_count,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
