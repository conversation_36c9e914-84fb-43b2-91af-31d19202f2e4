// 路口监控主应用程序
class IntersectionDashboard {
    constructor() {
        this.charts = null;
        this.scene = null;
        this.eventUpdateTimer = null;
        this.trafficLightTimer = null;
        this.init();
    }

    // 初始化应用
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initComponents());
        } else {
            this.initComponents();
        }
    }

    // 初始化组件
    initComponents() {
        // 初始化时间显示
        this.initTimeDisplay();

        // 初始化图表
        this.charts = new IntersectionCharts();

        // 初始化3D场景
        this.scene = new IntersectionScene();

        // 初始化事件监听
        this.initEventListeners();

        // 初始化动画效果
        this.initAnimations();

        // 开始数据模拟
        this.startDataSimulation();

        // 初始化信号灯控制
        this.initTrafficLightControl();

        console.log('交通监控平台已启动');
    }

    // 初始化时间显示
    initTimeDisplay() {
        const timeElement = document.getElementById('currentTime');
        if (!timeElement) return;

        const updateTime = () => {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('zh-CN', {
                month: '2-digit',
                day: '2-digit'
            });
            timeElement.textContent = `${dateString} ${timeString}`;
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    // 初始化事件监听
    initEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            if (this.charts) {
                this.charts.handleResize();
            }
            if (this.scene) {
                this.scene.handleResize();
            }
        });

        // 控制按钮点击
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleControlAction(btn);
            });
        });

        // 车辆标记点击
        document.querySelectorAll('.vehicle-marker, .incident-marker').forEach(marker => {
            marker.addEventListener('click', () => {
                this.handleMarkerClick(marker);
            });
        });

        // 设备状态点击
        document.querySelectorAll('.device-item').forEach(device => {
            device.addEventListener('click', () => {
                this.handleDeviceClick(device);
            });
        });
    }

    // 处理控制操作
    handleControlAction(button) {
        const action = button.textContent.trim();
        
        // 添加点击效果
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);

        console.log(`执行操作: ${action}`);
        
        switch (action) {
            case '信号灯控制':
                this.showControlDialog('信号灯控制', '正在调整信号灯时序...');
                break;
            case '事故处理':
                this.showControlDialog('事故处理', '已通知相关部门处理事故');
                break;
            case '流量调节':
                this.showControlDialog('流量调节', '正在优化交通流量分配...');
                break;
            case '紧急停止':
                this.showControlDialog('紧急停止', '已启动紧急交通管制', 'error');
                break;
        }
    }

    // 处理标记点击
    handleMarkerClick(marker) {
        const isVehicle = marker.classList.contains('vehicle-marker');
        const isIncident = marker.classList.contains('incident-marker');
        
        if (isVehicle) {
            const info = marker.querySelector('.vehicle-info');
            if (info) {
                const content = info.textContent.replace(/\s+/g, ' ').trim();
                this.showControlDialog('车辆信息', `详细信息: ${content}`, 'info');
            }
        } else if (isIncident) {
            const info = marker.querySelector('.incident-info');
            if (info) {
                const content = info.textContent.replace(/\s+/g, ' ').trim();
                this.showControlDialog('事件详情', `${content} - 正在处理中`, 'warning');
            }
        }
    }

    // 处理设备点击
    handleDeviceClick(device) {
        const deviceName = device.querySelector('.device-name').textContent;
        const deviceStatus = device.querySelector('.device-status-text').textContent;
        
        this.showControlDialog('设备状态', `${deviceName}: ${deviceStatus}`, 
            deviceStatus === '在线' || deviceStatus === '正常' ? 'success' : 'error');
    }

    // 初始化动画效果
    initAnimations() {
        // 为面板添加进入动画
        const panels = document.querySelectorAll('.data-panel');
        panels.forEach((panel, index) => {
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                panel.style.transition = 'all 0.6s ease';
                panel.style.opacity = '1';
                panel.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // 添加统计卡片动画
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'scale(0.9)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'scale(1)';
            }, 500 + index * 200);
        });
    }

    // 开始数据模拟
    startDataSimulation() {
        // 模拟车辆移动
        this.simulateVehicleMovement();

        // 模拟事件更新
        this.simulateEventUpdates();

        // 模拟设备状态变化
        this.simulateDeviceStatus();
    }

    // 模拟车辆移动
    simulateVehicleMovement() {
        const markers = document.querySelectorAll('.vehicle-marker');
        
        setInterval(() => {
            markers.forEach(marker => {
                const currentLeft = parseFloat(marker.style.left) || 30;
                const currentTop = parseFloat(marker.style.top) || 45;
                
                // 模拟沿道路移动
                let newLeft = currentLeft + (Math.random() - 0.5) * 5;
                let newTop = currentTop + (Math.random() - 0.5) * 5;
                
                // 限制在道路范围内
                newLeft = Math.max(20, Math.min(80, newLeft));
                newTop = Math.max(20, Math.min(80, newTop));
                
                marker.style.left = newLeft + '%';
                marker.style.top = newTop + '%';
            });
        }, 3000);
    }

    // 模拟事件更新
    simulateEventUpdates() {
        this.eventUpdateTimer = setInterval(() => {
            this.addNewEvent();
        }, 15000);
    }

    // 添加新事件
    addNewEvent() {
        const eventList = document.querySelector('.event-list');
        if (!eventList) return;

        const events = [
            { type: 'normal', desc: '车辆正常通过', location: '东西向主道' },
            { type: 'warning', desc: '检测到超速车辆', location: '南北向辅道' },
            { type: 'urgent', desc: '发现违法停车', location: '路口东南角' },
            { type: 'normal', desc: '信号灯切换正常', location: '主路口' },
            { type: 'warning', desc: '车流量异常增加', location: '全路段' }
        ];

        const event = events[Math.floor(Math.random() * events.length)];
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', { hour12: false });

        const eventElement = document.createElement('div');
        eventElement.className = `event-item ${event.type}`;
        eventElement.innerHTML = `
            <div class="event-time">${timeString}</div>
            <div class="event-desc">${event.desc}</div>
            <div class="event-location">${event.location}</div>
        `;

        // 添加到列表顶部
        eventList.insertBefore(eventElement, eventList.firstChild);

        // 限制事件数量
        const eventItems = eventList.querySelectorAll('.event-item');
        if (eventItems.length > 6) {
            eventList.removeChild(eventItems[eventItems.length - 1]);
        }

        // 添加新增动画
        eventElement.style.opacity = '0';
        eventElement.style.transform = 'translateX(-20px)';
        setTimeout(() => {
            eventElement.style.transition = 'all 0.3s ease';
            eventElement.style.opacity = '1';
            eventElement.style.transform = 'translateX(0)';
        }, 100);
    }

    // 模拟设备状态变化
    simulateDeviceStatus() {
        setInterval(() => {
            const deviceItems = document.querySelectorAll('.device-status-text');
            
            deviceItems.forEach(status => {
                if (Math.random() < 0.1) { // 10%概率状态变化
                    const isOnline = status.classList.contains('online');
                    
                    if (isOnline && Math.random() < 0.3) {
                        status.textContent = '离线';
                        status.className = 'device-status-text offline';
                    } else if (!isOnline && Math.random() < 0.7) {
                        status.textContent = status.parentElement.querySelector('.device-name').textContent.includes('信号灯') ? '正常' : '在线';
                        status.className = 'device-status-text online';
                    }
                }
            });
        }, 20000);
    }

    // 初始化信号灯控制
    initTrafficLightControl() {
        const trafficLight = document.querySelector('.traffic-light');
        if (!trafficLight) return;

        let currentLight = 0; // 0: 红, 1: 黄, 2: 绿
        let timer = 23;

        this.trafficLightTimer = setInterval(() => {
            timer--;
            
            if (timer <= 0) {
                // 切换信号灯
                const lights = trafficLight.querySelectorAll('.light');
                lights[currentLight].classList.remove('active');
                
                currentLight = (currentLight + 1) % 3;
                lights[currentLight].classList.add('active');
                
                // 重置计时器
                timer = currentLight === 0 ? 30 : (currentLight === 1 ? 5 : 25);
            }
            
            // 更新计时器显示
            const timerElement = trafficLight.querySelector('.light-timer');
            if (timerElement) {
                timerElement.textContent = timer + 's';
            }
        }, 1000);
    }

    // 显示控制对话框
    showControlDialog(title, message, type = 'info') {
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'control-dialog';
        dialog.innerHTML = `
            <div class="dialog-overlay"></div>
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>${title}</h3>
                    <button class="dialog-close">&times;</button>
                </div>
                <div class="dialog-body">
                    <div class="dialog-icon ${type}">
                        ${this.getDialogIcon(type)}
                    </div>
                    <div class="dialog-message">${message}</div>
                </div>
                <div class="dialog-footer">
                    <button class="dialog-btn confirm">确认</button>
                    <button class="dialog-btn cancel">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // 添加事件监听
        dialog.querySelector('.dialog-close').addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        dialog.querySelector('.confirm').addEventListener('click', () => {
            console.log(`确认操作: ${title}`);
            document.body.removeChild(dialog);
        });

        dialog.querySelector('.cancel').addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        dialog.querySelector('.dialog-overlay').addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        // 自动关闭
        setTimeout(() => {
            if (dialog.parentNode) {
                document.body.removeChild(dialog);
            }
        }, 5000);
    }

    // 获取对话框图标
    getDialogIcon(type) {
        const icons = {
            success: '✓',
            warning: '⚠',
            error: '✗',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }

    // 销毁应用
    destroy() {
        if (this.scene) {
            this.scene.destroy();
        }

        if (this.eventUpdateTimer) {
            clearInterval(this.eventUpdateTimer);
        }

        if (this.trafficLightTimer) {
            clearInterval(this.trafficLightTimer);
        }
        
        // 清理事件监听器
        window.removeEventListener('resize', this.handleResize);
    }
}

// 启动应用
const intersectionDashboard = new IntersectionDashboard();

// 导出到全局
window.IntersectionDashboard = IntersectionDashboard;
