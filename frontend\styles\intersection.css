/* 全局样式重置 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 50%, #1a237e 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}


/* 主容器 */

.intersection-dashboard {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: radial-gradient(circle at 30% 30%, rgba(63, 81, 181, 0.2) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(57, 73, 171, 0.2) 0%, transparent 50%), linear-gradient(135deg, #1a237e 0%, #3949ab 50%, #1a237e 100%);
}


/* 顶部标题栏 */

.header {
    height: 70px;
    background: linear-gradient(90deg, rgba(26, 35, 126, 0.9) 0%, rgba(57, 73, 171, 0.7) 50%, rgba(26, 35, 126, 0.9) 100%);
    border-bottom: 2px solid rgba(63, 81, 181, 0.4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    backdrop-filter: blur(10px);
}

.title {
    font-size: 32px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.header-info {
    display: flex;
    align-items: center;
    gap: 30px;
}

.location-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.time-display {
    font-size: 18px;
    color: #64b5f6;
    font-weight: bold;
}


/* 主内容区域 */

.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: 15px;
    padding: 15px;
    height: calc(100vh - 70px);
}


/* 数据面板通用样式 */

.data-panel {
    background: linear-gradient(145deg, rgba(26, 35, 126, 0.6), rgba(57, 73, 171, 0.3));
    border: 1px solid rgba(63, 81, 181, 0.4);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(63, 81, 181, 0.3);
}

.panel-header h3 {
    color: #64b5f6;
    font-size: 16px;
    font-weight: bold;
}


/* 左侧面板 */

.left-panel {
    display: flex;
    flex-direction: column;
}


/* 图表容器 */

.chart-container {
    height: 180px;
}

.chart {
    width: 100%;
    height: 100%;
}


/* 违法行为统计 */

.violation-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: rgba(26, 35, 126, 0.4);
    border-radius: 8px;
    border-left: 3px solid #ff5722;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #ff5722;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}


/* 中央场景区域 */

.center-panel {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.intersection-scene {
    flex: 1;
    background: linear-gradient(145deg, rgba(26, 35, 126, 0.6), rgba(57, 73, 171, 0.3));
    border: 1px solid rgba(63, 81, 181, 0.4);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.scene-3d {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 48%, rgba(63, 81, 181, 0.2) 50%, transparent 52%), linear-gradient(-45deg, transparent 48%, rgba(63, 81, 181, 0.2) 50%, transparent 52%), radial-gradient(circle at center, rgba(100, 181, 246, 0.1) 0%, transparent 70%), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="5" height="5" patternUnits="userSpaceOnUse"><path d="M 5 0 L 0 0 0 5" fill="none" stroke="rgba(63,81,181,0.2)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    position: relative;
}

.scene-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}


/* 车辆标记 */

.vehicle-marker {
    position: absolute;
    pointer-events: auto;
    cursor: pointer;
}

.vehicle-marker .marker-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4caf50;
    border: 2px solid #ffffff;
    box-shadow: 0 0 12px rgba(76, 175, 80, 0.6);
    animation: vehiclePulse 2s infinite;
}

.vehicle-info {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    white-space: nowrap;
    border: 1px solid rgba(63, 81, 181, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.vehicle-marker:hover .vehicle-info {
    opacity: 1;
}


/* 事故标记 */

.incident-marker .marker-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    animation: incidentBlink 1s infinite;
}

.incident-marker.accident .marker-dot {
    background: #f44336;
    box-shadow: 0 0 15px rgba(244, 67, 54, 0.8);
}

.incident-marker.warning .marker-dot {
    background: #ff9800;
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.8);
}

.incident-info {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(244, 67, 54, 0.9);
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 10px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.incident-marker:hover .incident-info {
    opacity: 1;
}


/* 信号灯 */

.traffic-light {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    background: rgba(0, 0, 0, 0.8);
    padding: 8px 6px;
    border-radius: 8px;
    border: 1px solid rgba(63, 81, 181, 0.3);
}

.light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.light.red {
    background: #f44336;
}

.light.yellow {
    background: #ffeb3b;
}

.light.green {
    background: #4caf50;
}

.light.active {
    opacity: 1;
    box-shadow: 0 0 10px currentColor;
}

.light-timer {
    font-size: 10px;
    color: #ffffff;
    margin-top: 4px;
    font-weight: bold;
}


/* 底部统计 */

.bottom-stats {
    height: 80px;
    display: flex;
    gap: 15px;
}

.stat-card {
    flex: 1;
    background: linear-gradient(145deg, rgba(26, 35, 126, 0.6), rgba(57, 73, 171, 0.3));
    border: 1px solid rgba(63, 81, 181, 0.4);
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-card.highlight {
    border-color: #64b5f6;
    background: linear-gradient(145deg, rgba(100, 181, 246, 0.2), rgba(57, 73, 171, 0.3));
}

.stat-card .stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #64b5f6;
    margin-bottom: 5px;
}

.stat-card.highlight .stat-value {
    color: #ffeb3b;
}

.stat-card .stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}


/* 右侧面板 */

.right-panel {
    display: flex;
    flex-direction: column;
}


/* 设备状态 */

.device-status {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.device-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    background: rgba(26, 35, 126, 0.3);
    border-radius: 8px;
}

.device-icon {
    font-size: 20px;
}

.device-info {
    flex: 1;
}

.device-name {
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 2px;
}

.device-status-text {
    font-size: 12px;
}

.device-status-text.online {
    color: #4caf50;
}

.device-status-text.offline {
    color: #f44336;
}


/* 事件列表 */

.event-list {
    max-height: 200px;
    overflow-y: auto;
}

.event-item {
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 6px;
    border-left: 3px solid;
}

.event-item.urgent {
    background: rgba(244, 67, 54, 0.1);
    border-left-color: #f44336;
}

.event-item.warning {
    background: rgba(255, 152, 0, 0.1);
    border-left-color: #ff9800;
}

.event-item.normal {
    background: rgba(76, 175, 80, 0.1);
    border-left-color: #4caf50;
}

.event-time {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 3px;
}

.event-desc {
    font-size: 13px;
    color: #ffffff;
    margin-bottom: 2px;
}

.event-location {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
}


/* 控制按钮 */

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn.primary {
    background: linear-gradient(45deg, #2196f3, #64b5f6);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(45deg, #607d8b, #90a4ae);
    color: white;
}

.control-btn.danger {
    background: linear-gradient(45deg, #f44336, #ef5350);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}


/* 动画效果 */

@keyframes vehiclePulse {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes incidentBlink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0.4;
    }
}


/* 滚动条样式 */

.event-list::-webkit-scrollbar {
    width: 4px;
}

.event-list::-webkit-scrollbar-track {
    background: rgba(63, 81, 181, 0.1);
}

.event-list::-webkit-scrollbar-thumb {
    background: rgba(63, 81, 181, 0.5);
    border-radius: 2px;
}


/* 控制对话框 */

.control-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.dialog-content {
    position: relative;
    background: linear-gradient(145deg, rgba(26, 35, 126, 0.9), rgba(57, 73, 171, 0.8));
    border: 1px solid rgba(63, 81, 181, 0.5);
    border-radius: 12px;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: dialogSlideIn 0.3s ease;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 15px;
    border-bottom: 1px solid rgba(63, 81, 181, 0.3);
}

.dialog-header h3 {
    color: #64b5f6;
    font-size: 18px;
    margin: 0;
}

.dialog-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.dialog-close:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.dialog-body {
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.dialog-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
}

.dialog-icon.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.dialog-icon.warning {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.dialog-icon.error {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.dialog-icon.info {
    background: rgba(100, 181, 246, 0.2);
    color: #64b5f6;
}

.dialog-message {
    flex: 1;
    color: #ffffff;
    font-size: 14px;
    line-height: 1.5;
}

.dialog-footer {
    padding: 15px 25px 25px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.dialog-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.dialog-btn.confirm {
    background: linear-gradient(45deg, #2196f3, #64b5f6);
    color: white;
}

.dialog-btn.cancel {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dialog-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}


/* 响应式设计 */

@media (max-width: 1400px) {
    .main-content {
        grid-template-columns: 260px 1fr 300px;
    }
}

@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 240px 1fr 280px;
    }
    .title {
        font-size: 28px;
    }
    .dialog-content {
        min-width: 350px;
        margin: 20px;
    }
}