# -*- coding: utf-8 -*-
"""
数据处理与分析模块
包含数据清洗、去噪、预处理、统计分析、机器学习算法等功能
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
from sklearn.ensemble import IsolationForest, RandomForestRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.cluster import DBSCAN, KMeans
from sklearn.metrics import mean_squared_error, mean_absolute_error
import scipy.stats as stats
from scipy.signal import savgol_filter, medfilt
import warnings
warnings.filterwarnings('ignore')

from models import db, TrafficFlow, VehicleSpeed, TrafficEvent

logger = logging.getLogger(__name__)

class DataPreprocessor:
    """数据预处理器"""

    def __init__(self):
        self.scalers = {}
        self.outlier_detectors = {}

    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        logger.info(f"开始数据清洗，原始数据量: {len(data)}")

        # 1. 删除重复数据
        data_cleaned = data.drop_duplicates()
        logger.info(f"删除重复数据后: {len(data_cleaned)}")

        # 2. 处理缺失值
        data_cleaned = self._handle_missing_values(data_cleaned)

        # 3. 数据类型转换
        data_cleaned = self._convert_data_types(data_cleaned)

        # 4. 异常值检测和处理
        data_cleaned = self._handle_outliers(data_cleaned)

        logger.info(f"数据清洗完成，最终数据量: {len(data_cleaned)}")
        return data_cleaned

    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 数值型字段用中位数填充
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if data[col].isnull().sum() > 0:
                median_value = data[col].median()
                data[col].fillna(median_value, inplace=True)
                logger.debug(f"字段 {col} 缺失值用中位数 {median_value} 填充")

        # 分类型字段用众数填充
        categorical_columns = data.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            if data[col].isnull().sum() > 0:
                mode_value = data[col].mode().iloc[0] if not data[col].mode().empty else 'unknown'
                data[col].fillna(mode_value, inplace=True)
                logger.debug(f"字段 {col} 缺失值用众数 {mode_value} 填充")

        return data

    def _convert_data_types(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据类型转换"""
        # 时间字段转换
        time_columns = ['record_time', 'created_at', 'updated_at']
        for col in time_columns:
            if col in data.columns:
                data[col] = pd.to_datetime(data[col], errors='coerce')

        # 数值字段转换
        numeric_columns = ['flow_count', 'speed', 'vehicle_length']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')

        return data

    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """异常值检测和处理"""
        numeric_columns = ['flow_count', 'speed']

        for col in numeric_columns:
            if col in data.columns:
                # 使用IQR方法检测异常值
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                # 标记异常值
                outliers = (data[col] < lower_bound) | (data[col] > upper_bound)
                outlier_count = outliers.sum()

                if outlier_count > 0:
                    # 用边界值替换异常值
                    data.loc[data[col] < lower_bound, col] = lower_bound
                    data.loc[data[col] > upper_bound, col] = upper_bound
                    logger.info(f"字段 {col} 检测到 {outlier_count} 个异常值并已处理")

        return data

    def denoise_data(self, data: pd.DataFrame, method: str = 'savgol') -> pd.DataFrame:
        """数据去噪"""
        logger.info(f"开始数据去噪，方法: {method}")

        numeric_columns = ['flow_count', 'speed']
        data_denoised = data.copy()

        for col in numeric_columns:
            if col in data.columns and len(data) > 10:
                if method == 'savgol':
                    # Savitzky-Golay滤波
                    window_length = min(11, len(data) if len(data) % 2 == 1 else len(data) - 1)
                    if window_length >= 3:
                        data_denoised[col] = savgol_filter(data[col], window_length, 3)
                elif method == 'median':
                    # 中值滤波
                    kernel_size = min(5, len(data))
                    if kernel_size >= 3:
                        data_denoised[col] = medfilt(data[col], kernel_size)
                elif method == 'moving_average':
                    # 移动平均
                    window = min(5, len(data))
                    data_denoised[col] = data[col].rolling(window=window, center=True).mean()
                    data_denoised[col].fillna(data[col], inplace=True)

        logger.info("数据去噪完成")
        return data_denoised

    def normalize_data(self, data: pd.DataFrame, method: str = 'standard') -> pd.DataFrame:
        """数据标准化"""
        logger.info(f"开始数据标准化，方法: {method}")

        numeric_columns = ['flow_count', 'speed']
        data_normalized = data.copy()

        for col in numeric_columns:
            if col in data.columns:
                if method == 'standard':
                    if col not in self.scalers:
                        self.scalers[col] = StandardScaler()
                    data_normalized[col] = self.scalers[col].fit_transform(data[[col]]).flatten()
                elif method == 'minmax':
                    if col not in self.scalers:
                        self.scalers[col] = MinMaxScaler()
                    data_normalized[col] = self.scalers[col].fit_transform(data[[col]]).flatten()

        logger.info("数据标准化完成")
        return data_normalized

class StatisticalAnalyzer:
    """统计分析器"""

    def __init__(self):
        pass

    def basic_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """基础统计分析"""
        logger.info("开始基础统计分析")

        stats_result = {}
        numeric_columns = data.select_dtypes(include=[np.number]).columns

        for col in numeric_columns:
            if col in data.columns:
                stats_result[col] = {
                    'count': int(data[col].count()),
                    'mean': float(data[col].mean()),
                    'median': float(data[col].median()),
                    'std': float(data[col].std()),
                    'min': float(data[col].min()),
                    'max': float(data[col].max()),
                    'q25': float(data[col].quantile(0.25)),
                    'q75': float(data[col].quantile(0.75)),
                    'skewness': float(stats.skew(data[col].dropna())),
                    'kurtosis': float(stats.kurtosis(data[col].dropna()))
                }

        logger.info("基础统计分析完成")
        return stats_result

    def correlation_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """相关性分析"""
        logger.info("开始相关性分析")

        numeric_data = data.select_dtypes(include=[np.number])

        # Pearson相关系数
        pearson_corr = numeric_data.corr(method='pearson')

        # Spearman相关系数
        spearman_corr = numeric_data.corr(method='spearman')

        correlation_result = {
            'pearson_correlation': pearson_corr.to_dict(),
            'spearman_correlation': spearman_corr.to_dict()
        }

        logger.info("相关性分析完成")
        return correlation_result

    def time_series_analysis(self, data: pd.DataFrame, time_col: str = 'record_time',
                           value_col: str = 'flow_count') -> Dict[str, Any]:
        """时间序列分析"""
        logger.info(f"开始时间序列分析: {value_col}")

        if time_col not in data.columns or value_col not in data.columns:
            logger.warning(f"缺少必要字段: {time_col} 或 {value_col}")
            return {}

        # 按时间排序
        data_sorted = data.sort_values(time_col)

        # 重采样到小时级别
        data_sorted.set_index(time_col, inplace=True)
        hourly_data = data_sorted[value_col].resample('H').mean()

        # 趋势分析
        trend_analysis = self._analyze_trend(hourly_data)

        # 季节性分析
        seasonal_analysis = self._analyze_seasonality(hourly_data)

        # 周期性分析
        periodic_analysis = self._analyze_periodicity(hourly_data)

        ts_result = {
            'trend_analysis': trend_analysis,
            'seasonal_analysis': seasonal_analysis,
            'periodic_analysis': periodic_analysis,
            'data_points': len(hourly_data),
            'time_range': {
                'start': hourly_data.index.min().isoformat(),
                'end': hourly_data.index.max().isoformat()
            }
        }

        logger.info("时间序列分析完成")
        return ts_result

    def _analyze_trend(self, series: pd.Series) -> Dict[str, Any]:
        """趋势分析"""
        if len(series) < 2:
            return {'trend': 'insufficient_data'}

        # 线性回归分析趋势
        x = np.arange(len(series))
        y = series.values

        # 去除NaN值
        valid_indices = ~np.isnan(y)
        x_valid = x[valid_indices]
        y_valid = y[valid_indices]

        if len(x_valid) < 2:
            return {'trend': 'insufficient_valid_data'}

        slope, intercept, r_value, p_value, std_err = stats.linregress(x_valid, y_valid)

        trend_direction = 'increasing' if slope > 0 else 'decreasing' if slope < 0 else 'stable'

        return {
            'trend': trend_direction,
            'slope': float(slope),
            'r_squared': float(r_value ** 2),
            'p_value': float(p_value),
            'significance': 'significant' if p_value < 0.05 else 'not_significant'
        }

    def _analyze_seasonality(self, series: pd.Series) -> Dict[str, Any]:
        """季节性分析"""
        if len(series) < 24:  # 至少需要24小时数据
            return {'seasonality': 'insufficient_data'}

        # 按小时分组分析
        hourly_pattern = series.groupby(series.index.hour).mean()

        # 计算变异系数
        cv = hourly_pattern.std() / hourly_pattern.mean() if hourly_pattern.mean() != 0 else 0

        # 找出高峰和低谷时段
        peak_hours = hourly_pattern.nlargest(3).index.tolist()
        valley_hours = hourly_pattern.nsmallest(3).index.tolist()

        return {
            'seasonality': 'detected' if cv > 0.2 else 'weak',
            'coefficient_of_variation': float(cv),
            'peak_hours': [int(h) for h in peak_hours],
            'valley_hours': [int(h) for h in valley_hours],
            'hourly_pattern': hourly_pattern.to_dict()
        }

    def _analyze_periodicity(self, series: pd.Series) -> Dict[str, Any]:
        """周期性分析"""
        if len(series) < 48:  # 至少需要48小时数据
            return {'periodicity': 'insufficient_data'}

        # 自相关分析
        autocorr_24h = series.autocorr(lag=24) if len(series) >= 24 else 0
        autocorr_12h = series.autocorr(lag=12) if len(series) >= 12 else 0

        # 判断周期性强度
        if autocorr_24h > 0.7:
            periodicity = 'strong_daily'
        elif autocorr_24h > 0.4:
            periodicity = 'moderate_daily'
        elif autocorr_12h > 0.5:
            periodicity = 'semi_daily'
        else:
            periodicity = 'weak'

        return {
            'periodicity': periodicity,
            'daily_autocorr': float(autocorr_24h),
            'semi_daily_autocorr': float(autocorr_12h)
        }

class MachineLearningAnalyzer:
    """机器学习分析器"""

    def __init__(self):
        self.models = {}
        self.preprocessor = DataPreprocessor()

    def traffic_flow_prediction(self, data: pd.DataFrame,
                              prediction_horizon: int = 24) -> Dict[str, Any]:
        """交通流量预测"""
        logger.info(f"开始交通流量预测，预测时长: {prediction_horizon}小时")

        if len(data) < 48:  # 至少需要48小时历史数据
            logger.warning("历史数据不足，无法进行预测")
            return {'error': 'insufficient_data'}

        # 数据预处理
        data_clean = self.preprocessor.clean_data(data.copy())

        # 特征工程
        features = self._create_time_features(data_clean)

        # 准备训练数据
        X, y = self._prepare_prediction_data(features, target_col='flow_count')

        if len(X) < 10:
            logger.warning("训练数据不足")
            return {'error': 'insufficient_training_data'}

        # 训练模型
        model = RandomForestRegressor(n_estimators=100, random_state=42)

        # 时间序列交叉验证
        train_size = int(len(X) * 0.8)
        X_train, X_test = X[:train_size], X[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]

        model.fit(X_train, y_train)

        # 模型评估
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)

        # 生成预测
        future_features = self._generate_future_features(features, prediction_horizon)
        predictions = model.predict(future_features)

        # 生成预测时间戳
        last_time = data_clean['record_time'].max()
        prediction_times = [last_time + timedelta(hours=i+1) for i in range(prediction_horizon)]

        prediction_result = {
            'predictions': [
                {
                    'time': time.isoformat(),
                    'predicted_flow': max(0, float(pred))  # 确保预测值非负
                }
                for time, pred in zip(prediction_times, predictions)
            ],
            'model_performance': {
                'mse': float(mse),
                'mae': float(mae),
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            },
            'feature_importance': dict(zip(
                [f'feature_{i}' for i in range(len(X_train[0]))],
                model.feature_importances_.tolist()
            ))
        }

        logger.info("交通流量预测完成")
        return prediction_result

    def congestion_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """拥堵成因分析"""
        logger.info("开始拥堵成因分析")

        if len(data) < 10:
            return {'error': 'insufficient_data'}

        # 数据预处理
        data_clean = self.preprocessor.clean_data(data.copy())

        # 定义拥堵阈值（基于速度和流量）
        speed_threshold = data_clean['speed'].quantile(0.2) if 'speed' in data_clean.columns else 30
        flow_threshold = data_clean['flow_count'].quantile(0.8) if 'flow_count' in data_clean.columns else 100

        # 识别拥堵时段
        congestion_mask = (data_clean['speed'] < speed_threshold) & (data_clean['flow_count'] > flow_threshold)
        congestion_data = data_clean[congestion_mask]

        if len(congestion_data) == 0:
            return {'message': 'no_congestion_detected'}

        # 拥堵特征分析
        congestion_features = self._analyze_congestion_features(congestion_data, data_clean)

        # 拥堵模式识别
        congestion_patterns = self._identify_congestion_patterns(congestion_data)

        # 拥堵成因分析
        congestion_causes = self._analyze_congestion_causes(congestion_data, data_clean)

        analysis_result = {
            'congestion_statistics': {
                'total_congestion_events': len(congestion_data),
                'congestion_rate': len(congestion_data) / len(data_clean),
                'average_congestion_duration': self._calculate_avg_duration(congestion_data),
                'peak_congestion_hours': self._find_peak_hours(congestion_data)
            },
            'congestion_features': congestion_features,
            'congestion_patterns': congestion_patterns,
            'congestion_causes': congestion_causes
        }

        logger.info("拥堵成因分析完成")
        return analysis_result

    def anomaly_detection(self, data: pd.DataFrame) -> Dict[str, Any]:
        """异常检测"""
        logger.info("开始异常检测")

        if len(data) < 20:
            return {'error': 'insufficient_data'}

        # 数据预处理
        data_clean = self.preprocessor.clean_data(data.copy())

        # 特征选择
        feature_columns = ['flow_count', 'speed']
        available_features = [col for col in feature_columns if col in data_clean.columns]

        if not available_features:
            return {'error': 'no_suitable_features'}

        # 准备特征数据
        X = data_clean[available_features].values

        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 使用Isolation Forest进行异常检测
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomaly_labels = iso_forest.fit_predict(X_scaled)

        # 异常分数
        anomaly_scores = iso_forest.decision_function(X_scaled)

        # 标记异常点
        data_clean['is_anomaly'] = anomaly_labels == -1
        data_clean['anomaly_score'] = anomaly_scores

        # 异常统计
        anomaly_data = data_clean[data_clean['is_anomaly']]

        anomaly_result = {
            'anomaly_statistics': {
                'total_anomalies': len(anomaly_data),
                'anomaly_rate': len(anomaly_data) / len(data_clean),
                'most_anomalous_features': self._identify_anomalous_features(anomaly_data, available_features)
            },
            'anomaly_details': [
                {
                    'timestamp': row['record_time'].isoformat() if pd.notna(row['record_time']) else None,
                    'location': row.get('location_name', 'unknown'),
                    'anomaly_score': float(row['anomaly_score']),
                    'features': {col: float(row[col]) for col in available_features}
                }
                for _, row in anomaly_data.head(10).iterrows()
            ]
        }

        logger.info("异常检测完成")
        return anomaly_result

    def _create_time_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建时间特征"""
        features = data.copy()

        if 'record_time' in features.columns:
            features['hour'] = features['record_time'].dt.hour
            features['day_of_week'] = features['record_time'].dt.dayofweek
            features['month'] = features['record_time'].dt.month
            features['is_weekend'] = features['day_of_week'].isin([5, 6]).astype(int)
            features['is_rush_hour'] = features['hour'].isin([7, 8, 9, 17, 18, 19]).astype(int)

        return features

    def _prepare_prediction_data(self, data: pd.DataFrame, target_col: str,
                               lookback: int = 24) -> Tuple[np.ndarray, np.ndarray]:
        """准备预测数据"""
        # 选择数值特征
        feature_cols = ['hour', 'day_of_week', 'month', 'is_weekend', 'is_rush_hour']
        available_features = [col for col in feature_cols if col in data.columns]

        if target_col not in data.columns:
            raise ValueError(f"目标列 {target_col} 不存在")

        # 创建滑动窗口数据
        X, y = [], []

        for i in range(lookback, len(data)):
            # 历史特征
            hist_features = data[available_features].iloc[i-lookback:i].values.flatten()
            # 历史目标值
            hist_targets = data[target_col].iloc[i-lookback:i].values
            # 当前时间特征
            current_features = data[available_features].iloc[i].values

            # 组合特征
            combined_features = np.concatenate([hist_features, hist_targets, current_features])

            X.append(combined_features)
            y.append(data[target_col].iloc[i])

        return np.array(X), np.array(y)

    def _generate_future_features(self, data: pd.DataFrame, horizon: int) -> np.ndarray:
        """生成未来时间特征"""
        last_time = data['record_time'].max()
        future_features = []

        for i in range(1, horizon + 1):
            future_time = last_time + timedelta(hours=i)

            features = [
                future_time.hour,
                future_time.weekday(),
                future_time.month,
                1 if future_time.weekday() in [5, 6] else 0,  # is_weekend
                1 if future_time.hour in [7, 8, 9, 17, 18, 19] else 0  # is_rush_hour
            ]

            # 添加历史数据作为特征（简化版本）
            recent_data = data.tail(24)
            hist_features = recent_data[['flow_count']].values.flatten()

            # 组合特征
            combined = np.concatenate([hist_features, features])
            future_features.append(combined)

        return np.array(future_features)

    def _analyze_congestion_features(self, congestion_data: pd.DataFrame,
                                   all_data: pd.DataFrame) -> Dict[str, Any]:
        """分析拥堵特征"""
        features = {}

        if 'speed' in congestion_data.columns:
            features['average_congestion_speed'] = float(congestion_data['speed'].mean())
            features['min_congestion_speed'] = float(congestion_data['speed'].min())

        if 'flow_count' in congestion_data.columns:
            features['average_congestion_flow'] = float(congestion_data['flow_count'].mean())
            features['max_congestion_flow'] = float(congestion_data['flow_count'].max())

        return features

    def _identify_congestion_patterns(self, congestion_data: pd.DataFrame) -> Dict[str, Any]:
        """识别拥堵模式"""
        patterns = {}

        if 'record_time' in congestion_data.columns:
            # 按小时统计拥堵频率
            hourly_congestion = congestion_data.groupby(
                congestion_data['record_time'].dt.hour
            ).size()

            patterns['peak_congestion_hours'] = hourly_congestion.nlargest(3).index.tolist()

            # 按星期统计
            if len(congestion_data) > 7:
                weekly_congestion = congestion_data.groupby(
                    congestion_data['record_time'].dt.dayofweek
                ).size()
                patterns['peak_congestion_days'] = weekly_congestion.nlargest(2).index.tolist()

        return patterns

    def _analyze_congestion_causes(self, congestion_data: pd.DataFrame,
                                 all_data: pd.DataFrame) -> Dict[str, Any]:
        """分析拥堵成因"""
        causes = {}

        # 流量过高导致的拥堵
        high_flow_threshold = all_data['flow_count'].quantile(0.9) if 'flow_count' in all_data.columns else 150
        high_flow_congestion = len(congestion_data[congestion_data['flow_count'] > high_flow_threshold])

        causes['high_flow_ratio'] = high_flow_congestion / len(congestion_data) if len(congestion_data) > 0 else 0

        # 时间相关的拥堵
        if 'record_time' in congestion_data.columns:
            rush_hour_mask = congestion_data['record_time'].dt.hour.isin([7, 8, 9, 17, 18, 19])
            rush_hour_congestion = rush_hour_mask.sum()
            causes['rush_hour_ratio'] = rush_hour_congestion / len(congestion_data) if len(congestion_data) > 0 else 0

        return causes

    def _calculate_avg_duration(self, congestion_data: pd.DataFrame) -> float:
        """计算平均拥堵持续时间"""
        if 'record_time' not in congestion_data.columns or len(congestion_data) < 2:
            return 0.0

        # 简化计算：假设连续的记录表示持续的拥堵
        durations = []
        current_duration = 1

        sorted_data = congestion_data.sort_values('record_time')
        times = sorted_data['record_time'].values

        for i in range(1, len(times)):
            time_diff = (times[i] - times[i-1]).total_seconds() / 3600  # 小时
            if time_diff <= 2:  # 2小时内认为是连续拥堵
                current_duration += 1
            else:
                durations.append(current_duration)
                current_duration = 1

        durations.append(current_duration)

        return float(np.mean(durations)) if durations else 0.0

    def _find_peak_hours(self, congestion_data: pd.DataFrame) -> List[int]:
        """找出拥堵高峰时段"""
        if 'record_time' not in congestion_data.columns:
            return []

        hourly_counts = congestion_data.groupby(
            congestion_data['record_time'].dt.hour
        ).size()

        return hourly_counts.nlargest(3).index.tolist()

    def _identify_anomalous_features(self, anomaly_data: pd.DataFrame,
                                   features: List[str]) -> Dict[str, float]:
        """识别最异常的特征"""
        anomalous_features = {}

        for feature in features:
            if feature in anomaly_data.columns:
                # 计算特征的异常程度（与正常数据的偏差）
                feature_std = anomaly_data[feature].std()
                anomalous_features[feature] = float(feature_std) if not np.isnan(feature_std) else 0.0

        return anomalous_features

class TrafficPatternAnalyzer:
    """交通模式分析器"""

    def __init__(self):
        self.ml_analyzer = MachineLearningAnalyzer()
        self.stat_analyzer = StatisticalAnalyzer()

    def analyze_traffic_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """综合交通模式分析"""
        logger.info("开始综合交通模式分析")

        analysis_result = {
            'basic_statistics': self.stat_analyzer.basic_statistics(data),
            'time_series_analysis': self.stat_analyzer.time_series_analysis(data),
            'correlation_analysis': self.stat_analyzer.correlation_analysis(data),
            'flow_prediction': self.ml_analyzer.traffic_flow_prediction(data),
            'congestion_analysis': self.ml_analyzer.congestion_analysis(data),
            'anomaly_detection': self.ml_analyzer.anomaly_detection(data),
            'analysis_timestamp': datetime.utcnow().isoformat()
        }

        logger.info("综合交通模式分析完成")
        return analysis_result

# 创建全局分析器实例
traffic_analyzer = TrafficPatternAnalyzer()