// 3D场景管理类
class TrafficScene {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.vehicles = [];
        this.animationId = null;
        this.init();
    }

    // 初始化3D场景
    init() {
        const container = document.getElementById('roadScene');
        if (!container) return;

        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a1428);

        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75,
            container.clientWidth / container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 10, 15);
        this.camera.lookAt(0, 0, 0);

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true, 
            alpha: true 
        });
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.setClearColor(0x000000, 0);
        container.appendChild(this.renderer.domElement);

        // 添加光源
        this.addLights();

        // 创建道路
        this.createRoad();

        // 创建车辆
        this.createVehicles();

        // 开始渲染循环
        this.animate();

        // 监听窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
    }

    // 添加光源
    addLights() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // 方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);

        // 点光源（模拟路灯）
        const pointLight1 = new THREE.PointLight(0x00d4ff, 1, 50);
        pointLight1.position.set(-5, 5, 5);
        this.scene.add(pointLight1);

        const pointLight2 = new THREE.PointLight(0x00ff88, 1, 50);
        pointLight2.position.set(5, 5, -5);
        this.scene.add(pointLight2);
    }

    // 创建道路
    createRoad() {
        // 主道路
        const roadGeometry = new THREE.PlaneGeometry(20, 40);
        const roadMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            transparent: true,
            opacity: 0.8
        });
        const road = new THREE.Mesh(roadGeometry, roadMaterial);
        road.rotation.x = -Math.PI / 2;
        this.scene.add(road);

        // 道路标线
        this.createRoadMarkings();

        // 路边设施
        this.createRoadside();
    }

    // 创建道路标线
    createRoadMarkings() {
        const lineGeometry = new THREE.BoxGeometry(0.2, 0.01, 2);
        const lineMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

        // 中央分隔线
        for (let i = -18; i <= 18; i += 4) {
            const line = new THREE.Mesh(lineGeometry, lineMaterial);
            line.position.set(0, 0.01, i);
            this.scene.add(line);
        }

        // 车道线
        for (let x = -5; x <= 5; x += 10) {
            for (let i = -18; i <= 18; i += 6) {
                const line = new THREE.Mesh(lineGeometry, lineMaterial);
                line.position.set(x, 0.01, i);
                this.scene.add(line);
            }
        }
    }

    // 创建路边设施
    createRoadside() {
        // 路灯
        const poleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 4);
        const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });

        for (let i = -15; i <= 15; i += 10) {
            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
            pole.position.set(-12, 2, i);
            this.scene.add(pole);

            const pole2 = new THREE.Mesh(poleGeometry, poleMaterial);
            pole2.position.set(12, 2, i);
            this.scene.add(pole2);

            // 路灯光源
            const lightGeometry = new THREE.SphereGeometry(0.3);
            const lightMaterial = new THREE.MeshBasicMaterial({ 
                color: 0xffff88,
                emissive: 0xffff88,
                emissiveIntensity: 0.5
            });
            const light = new THREE.Mesh(lightGeometry, lightMaterial);
            light.position.set(-12, 4, i);
            this.scene.add(light);

            const light2 = new THREE.Mesh(lightGeometry, lightMaterial);
            light2.position.set(12, 4, i);
            this.scene.add(light2);
        }
    }

    // 创建车辆
    createVehicles() {
        const vehicleGeometry = new THREE.BoxGeometry(1.5, 0.8, 3);
        const vehicleColors = [0x00d4ff, 0x00ff88, 0xff6b6b, 0xffd700, 0x9c88ff];

        for (let i = 0; i < 8; i++) {
            const color = vehicleColors[Math.floor(Math.random() * vehicleColors.length)];
            const vehicleMaterial = new THREE.MeshLambertMaterial({ color });
            const vehicle = new THREE.Mesh(vehicleGeometry, vehicleMaterial);

            // 随机位置
            vehicle.position.x = (Math.random() - 0.5) * 15;
            vehicle.position.y = 0.4;
            vehicle.position.z = (Math.random() - 0.5) * 30;

            // 随机速度
            vehicle.userData = {
                speed: Math.random() * 0.1 + 0.05,
                direction: Math.random() > 0.5 ? 1 : -1,
                lane: Math.floor(Math.random() * 3) - 1 // -1, 0, 1 对应三个车道
            };

            this.vehicles.push(vehicle);
            this.scene.add(vehicle);
        }
    }

    // 更新车辆位置
    updateVehicles() {
        this.vehicles.forEach(vehicle => {
            // 沿Z轴移动
            vehicle.position.z += vehicle.userData.speed * vehicle.userData.direction;

            // 边界检查，重置位置
            if (vehicle.position.z > 20) {
                vehicle.position.z = -20;
                vehicle.position.x = vehicle.userData.lane * 3 + (Math.random() - 0.5) * 2;
            } else if (vehicle.position.z < -20) {
                vehicle.position.z = 20;
                vehicle.position.x = vehicle.userData.lane * 3 + (Math.random() - 0.5) * 2;
            }

            // 添加轻微的左右摆动
            vehicle.position.x += (Math.random() - 0.5) * 0.01;
        });
    }

    // 动画循环
    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());

        // 更新车辆
        this.updateVehicles();

        // 相机轻微摆动
        this.camera.position.x = Math.sin(Date.now() * 0.0005) * 2;
        this.camera.lookAt(0, 0, 0);

        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }

    // 处理窗口大小变化
    handleResize() {
        const container = document.getElementById('roadScene');
        if (!container) return;

        this.camera.aspect = container.clientWidth / container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(container.clientWidth, container.clientHeight);
    }

    // 销毁场景
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
    }
}

// 导出场景类
window.TrafficScene = TrafficScene;
