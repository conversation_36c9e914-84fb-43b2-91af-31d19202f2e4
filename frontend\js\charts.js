// 图表配置和初始化
class TrafficCharts {
    constructor() {
        this.charts = {};
        this.initCharts();
        this.startDataUpdate();
    }

    // 初始化所有图表
    initCharts() {
        this.initTrafficFlowChart();
        this.initTrafficStatsChart();
    }

    // 初始化车流量趋势图
    initTrafficFlowChart() {
        const chartDom = document.getElementById('trafficFlowChart');
        if (!chartDom) return;

        this.charts.trafficFlow = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            grid: {
                left: '10%',
                right: '10%',
                top: '15%',
                bottom: '15%'
            },
            xAxis: {
                type: 'category',
                data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                axisLine: {
                    lineStyle: { color: 'rgba(0, 150, 255, 0.3)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.6)',
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: { color: 'rgba(0, 150, 255, 0.3)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.6)',
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: { color: 'rgba(0, 150, 255, 0.1)' }
                }
            },
            series: [{
                name: '车流量',
                type: 'line',
                data: [120, 80, 300, 450, 380, 420, 200],
                smooth: true,
                lineStyle: {
                    color: '#00ff88',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                            { offset: 0, color: 'rgba(0, 255, 136, 0.3)' },
                            { offset: 1, color: 'rgba(0, 255, 136, 0.05)' }
                        ]
                    }
                },
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#00ff88',
                    borderColor: '#ffffff',
                    borderWidth: 2
                }
            }]
        };

        this.charts.trafficFlow.setOption(option);
    }

    // 初始化车流量统计柱状图
    initTrafficStatsChart() {
        const chartDom = document.getElementById('trafficStatsChart');
        if (!chartDom) return;

        this.charts.trafficStats = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            grid: {
                left: '15%',
                right: '10%',
                top: '15%',
                bottom: '15%'
            },
            xAxis: {
                type: 'category',
                data: ['小型车', '中型车', '大型车', '货车', '客车'],
                axisLine: {
                    lineStyle: { color: 'rgba(0, 150, 255, 0.3)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.6)',
                    fontSize: 10,
                    rotate: 0
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: { color: 'rgba(0, 150, 255, 0.3)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.6)',
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: { color: 'rgba(0, 150, 255, 0.1)' }
                }
            },
            series: [{
                name: '车辆数量',
                type: 'bar',
                data: [
                    { value: 850, itemStyle: { color: '#00d4ff' } },
                    { value: 320, itemStyle: { color: '#00ff88' } },
                    { value: 180, itemStyle: { color: '#ffd700' } },
                    { value: 95, itemStyle: { color: '#ff6b6b' } },
                    { value: 45, itemStyle: { color: '#9c88ff' } }
                ],
                barWidth: '60%',
                itemStyle: {
                    borderRadius: [4, 4, 0, 0]
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };

        this.charts.trafficStats.setOption(option);
    }

    // 更新图表数据
    updateChartData() {
        // 更新车流量趋势图
        if (this.charts.trafficFlow) {
            const newData = this.generateTrafficFlowData();
            this.charts.trafficFlow.setOption({
                series: [{
                    data: newData
                }]
            });
        }

        // 更新车流量统计图
        if (this.charts.trafficStats) {
            const newData = this.generateTrafficStatsData();
            this.charts.trafficStats.setOption({
                series: [{
                    data: newData
                }]
            });
        }
    }

    // 生成模拟车流量数据
    generateTrafficFlowData() {
        return Array.from({ length: 7 }, () => 
            Math.floor(Math.random() * 400) + 100
        );
    }

    // 生成模拟统计数据
    generateTrafficStatsData() {
        return [
            { value: Math.floor(Math.random() * 200) + 700, itemStyle: { color: '#00d4ff' } },
            { value: Math.floor(Math.random() * 100) + 250, itemStyle: { color: '#00ff88' } },
            { value: Math.floor(Math.random() * 80) + 120, itemStyle: { color: '#ffd700' } },
            { value: Math.floor(Math.random() * 50) + 70, itemStyle: { color: '#ff6b6b' } },
            { value: Math.floor(Math.random() * 30) + 30, itemStyle: { color: '#9c88ff' } }
        ];
    }

    // 开始数据更新定时器
    startDataUpdate() {
        // 每30秒更新一次数据
        setInterval(() => {
            this.updateChartData();
        }, 30000);

        // 每5秒更新实时数据显示
        setInterval(() => {
            this.updateRealtimeData();
        }, 5000);
    }

    // 更新实时数据显示
    updateRealtimeData() {
        const dataElements = {
            traffic: document.querySelector('.realtime-data .data-row:nth-child(1) .data-value'),
            speed: document.querySelector('.realtime-data .data-row:nth-child(2) .data-value'),
            occupancy: document.querySelector('.realtime-data .data-row:nth-child(3) .data-value'),
            incidents: document.querySelector('.realtime-data .data-row:nth-child(4) .data-value')
        };

        if (dataElements.traffic) {
            const traffic = Math.floor(Math.random() * 500) + 1000;
            dataElements.traffic.innerHTML = `${traffic.toLocaleString()} <small>辆/小时</small>`;
        }

        if (dataElements.speed) {
            const speed = Math.floor(Math.random() * 30) + 65;
            dataElements.speed.innerHTML = `${speed} <small>km/h</small>`;
        }

        if (dataElements.occupancy) {
            const occupancy = (Math.random() * 20 + 15).toFixed(1);
            dataElements.occupancy.innerHTML = `${occupancy} <small>%</small>`;
        }

        if (dataElements.incidents) {
            const incidents = Math.floor(Math.random() * 5);
            dataElements.incidents.innerHTML = `${incidents} <small>起</small>`;
            dataElements.incidents.className = incidents > 2 ? 'data-value warning' : 'data-value';
        }
    }

    // 响应式处理
    handleResize() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });
    }
}

// 导出图表类
window.TrafficCharts = TrafficCharts;
