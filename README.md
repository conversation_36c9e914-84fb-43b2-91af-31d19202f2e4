# 智慧城市交通流量可视化数据分析系统

## 项目简介

本项目是一个基于前后端分离架构的智慧城市交通流量可视化数据分析系统，旨在通过现代化的Web技术栈，为城市交通管理提供直观、高效的数据分析和可视化解决方案。

## 技术架构

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI框架**: Tailwind CSS
- **可视化库**: ECharts + D3.js
- **状态管理**: Zustand
- **HTTP客户端**: Axios

### 后端技术栈
- **语言**: Python 3.9+
- **框架**: Flask + Flask-RESTful
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: SQLAlchemy
- **数据处理**: Pandas + NumPy
- **API文档**: Flask-RESTX (Swagger)

## 核心功能

### 1. 数据管理
- 交通流量数据导入和存储
- 数据清洗和预处理
- 历史数据管理

### 2. 可视化展示
- 实时交通流量监控
- 历史趋势分析图表
- 地理位置热力图
- 多维度数据对比

### 3. 交互分析
- 时间范围筛选
- 地点和路段筛选
- 数据钻取和下钻
- 自定义查询

### 4. 报告生成
- 数据报告导出
- 图表截图保存
- PDF报告生成

## 项目结构

```
智慧城市交通流量/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── services/       # API服务
│   │   ├── stores/         # 状态管理
│   │   ├── types/          # TypeScript类型定义
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                 # Python Flask后端
│   ├── app/
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # API路由
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── migrations/         # 数据库迁移
│   ├── tests/              # 测试文件
│   └── requirements.txt
├── database/               # 数据库相关
│   ├── init.sql           # 初始化脚本
│   └── sample_data/       # 示例数据
├── docs/                   # 项目文档
└── docker-compose.yml      # Docker配置
```

## 快速开始

### 环境要求
- Node.js 16+
- Python 3.9+
- Git

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 智慧城市交通流量
```

2. **后端设置**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
flask db upgrade
flask run
```

3. **前端设置**
```bash
cd frontend
npm install
npm run dev
```

4. **访问应用**
- 前端: http://localhost:5173
- 后端API: http://localhost:5000
- API文档: http://localhost:5000/docs

## 开发指南

### 代码规范
- 前端遵循ESLint + Prettier配置
- 后端遵循PEP 8规范
- 提交信息遵循Conventional Commits

### 测试
```bash
# 前端测试
cd frontend && npm test

# 后端测试
cd backend && python -m pytest
```

## 部署

### Docker部署
```bash
docker-compose up -d
```

### 生产环境
详见 `docs/deployment.md`

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系项目维护者。
