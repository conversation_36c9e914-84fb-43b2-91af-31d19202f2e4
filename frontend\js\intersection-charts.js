// 路口监控图表管理类
class IntersectionCharts {
    constructor() {
        this.charts = {};
        this.initCharts();
        this.startDataUpdate();
    }

    // 初始化所有图表
    initCharts() {
        this.initHourlyTrafficChart();
        this.initSpeedAnalysisChart();
    }

    // 初始化小时车流量图表
    initHourlyTrafficChart() {
        const chartDom = document.getElementById('hourlyTrafficChart');
        if (!chartDom) return;

        this.charts.hourlyTraffic = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            grid: {
                left: '15%',
                right: '10%',
                top: '15%',
                bottom: '20%'
            },
            xAxis: {
                type: 'category',
                data: ['6时', '8时', '10时', '12时', '14时', '16时', '18时', '20时'],
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.2)' }
                }
            },
            series: [
                {
                    name: '东西向',
                    type: 'bar',
                    data: [320, 580, 450, 620, 580, 720, 890, 650],
                    itemStyle: {
                        color: '#64b5f6',
                        borderRadius: [2, 2, 0, 0]
                    },
                    barWidth: '35%'
                },
                {
                    name: '南北向',
                    type: 'bar',
                    data: [280, 520, 380, 560, 520, 680, 820, 590],
                    itemStyle: {
                        color: '#42a5f5',
                        borderRadius: [2, 2, 0, 0]
                    },
                    barWidth: '35%'
                }
            ],
            legend: {
                data: ['东西向', '南北向'],
                textStyle: {
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: 11
                },
                top: '5%'
            }
        };

        this.charts.hourlyTraffic.setOption(option);
    }

    // 初始化车速分析图表
    initSpeedAnalysisChart() {
        const chartDom = document.getElementById('speedAnalysisChart');
        if (!chartDom) return;

        this.charts.speedAnalysis = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            grid: {
                left: '10%',
                right: '10%',
                top: '15%',
                bottom: '15%'
            },
            xAxis: {
                type: 'category',
                data: ['0-20', '20-40', '40-60', '60-80', '80+'],
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                },
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.2)' }
                }
            },
            series: [{
                name: '车辆数量',
                type: 'line',
                data: [45, 180, 320, 280, 85],
                smooth: true,
                lineStyle: {
                    color: '#81c784',
                    width: 3
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                            { offset: 0, color: 'rgba(129, 199, 132, 0.4)' },
                            { offset: 1, color: 'rgba(129, 199, 132, 0.05)' }
                        ]
                    }
                },
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#81c784',
                    borderColor: '#ffffff',
                    borderWidth: 2
                }
            }]
        };

        this.charts.speedAnalysis.setOption(option);
    }

    // 更新图表数据
    updateChartData() {
        // 更新小时车流量图表
        if (this.charts.hourlyTraffic) {
            const eastWestData = this.generateTrafficData();
            const northSouthData = this.generateTrafficData();
            
            this.charts.hourlyTraffic.setOption({
                series: [
                    { data: eastWestData },
                    { data: northSouthData }
                ]
            });
        }

        // 更新车速分析图表
        if (this.charts.speedAnalysis) {
            const speedData = this.generateSpeedData();
            this.charts.speedAnalysis.setOption({
                series: [{ data: speedData }]
            });
        }
    }

    // 生成模拟车流量数据
    generateTrafficData() {
        return Array.from({ length: 8 }, () => 
            Math.floor(Math.random() * 300) + 300
        );
    }

    // 生成模拟车速数据
    generateSpeedData() {
        return [
            Math.floor(Math.random() * 30) + 30,   // 0-20
            Math.floor(Math.random() * 50) + 150,  // 20-40
            Math.floor(Math.random() * 80) + 280,  // 40-60
            Math.floor(Math.random() * 60) + 240,  // 60-80
            Math.floor(Math.random() * 40) + 60    // 80+
        ];
    }

    // 开始数据更新定时器
    startDataUpdate() {
        // 每30秒更新一次图表数据
        setInterval(() => {
            this.updateChartData();
        }, 30000);

        // 每5秒更新违法行为统计
        setInterval(() => {
            this.updateViolationStats();
        }, 5000);

        // 每10秒更新底部统计数据
        setInterval(() => {
            this.updateBottomStats();
        }, 10000);
    }

    // 更新违法行为统计
    updateViolationStats() {
        const statItems = document.querySelectorAll('.violation-stats .stat-number');
        
        if (statItems.length >= 3) {
            // 违法停车
            statItems[0].textContent = Math.floor(Math.random() * 20) + 40;
            
            // 超速行驶
            statItems[1].textContent = Math.floor(Math.random() * 15) + 15;
            
            // 闯红灯
            statItems[2].textContent = Math.floor(Math.random() * 10) + 5;
        }
    }

    // 更新底部统计数据
    updateBottomStats() {
        const statCards = document.querySelectorAll('.stat-card .stat-value');
        
        if (statCards.length >= 2) {
            // 当前事故数量
            const accidents = Math.floor(Math.random() * 5);
            statCards[0].textContent = accidents;
            
            // 通行车辆/分钟
            const vehicles = Math.floor(Math.random() * 30) + 50;
            statCards[1].textContent = vehicles;
        }
    }

    // 响应式处理
    handleResize() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });
    }
}

// 导出图表类
window.IntersectionCharts = IntersectionCharts;
