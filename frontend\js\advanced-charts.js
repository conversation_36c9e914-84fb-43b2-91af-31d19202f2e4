// 高级可视化组件库
class AdvancedCharts {
    constructor() {
        this.charts = {};
        this.heatmapData = [];
        this.mapInstance = null;
    }

    // 创建交通流量热力图
    createTrafficHeatmap(containerId, data) {
        const chartDom = document.getElementById(containerId);
        if (!chartDom) return null;

        const chart = echarts.init(chartDom);
        
        // 处理热力图数据
        const heatmapData = this.processHeatmapData(data);
        
        const option = {
            backgroundColor: 'transparent',
            title: {
                text: '交通流量热力图',
                textStyle: {
                    color: '#64b5f6',
                    fontSize: 16
                },
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    return `时间: ${params.data[0]}:00<br/>
                            位置: 路段${params.data[1]}<br/>
                            流量: ${params.data[2]} 辆/小时`;
                }
            },
            grid: {
                left: '10%',
                right: '10%',
                top: '15%',
                bottom: '15%'
            },
            xAxis: {
                type: 'category',
                data: Array.from({length: 24}, (_, i) => i + 'h'),
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 10
                },
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                }
            },
            yAxis: {
                type: 'category',
                data: ['路段1', '路段2', '路段3', '路段4', '路段5', '路段6'],
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 10
                },
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                }
            },
            visualMap: {
                min: 0,
                max: 500,
                calculable: true,
                orient: 'horizontal',
                left: 'center',
                bottom: '5%',
                textStyle: {
                    color: 'rgba(255, 255, 255, 0.8)'
                },
                inRange: {
                    color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', 
                           '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
                }
            },
            series: [{
                name: '交通流量',
                type: 'heatmap',
                data: heatmapData,
                label: {
                    show: false
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };

        chart.setOption(option);
        this.charts[containerId] = chart;
        return chart;
    }

    // 创建实时流量折线图
    createRealTimeFlowChart(containerId, data) {
        const chartDom = document.getElementById(containerId);
        if (!chartDom) return null;

        const chart = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            title: {
                text: '实时交通流量',
                textStyle: {
                    color: '#64b5f6',
                    fontSize: 16
                },
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            },
            legend: {
                data: ['东西向', '南北向', '预测值'],
                textStyle: {
                    color: 'rgba(255, 255, 255, 0.8)'
                },
                top: '8%'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: this.generateTimeLabels(),
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)'
                },
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)'
                },
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                },
                splitLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.2)' }
                }
            },
            series: [
                {
                    name: '东西向',
                    type: 'line',
                    smooth: true,
                    data: this.generateFlowData(),
                    lineStyle: {
                        color: '#00d4ff',
                        width: 3
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                                { offset: 1, color: 'rgba(0, 212, 255, 0.05)' }
                            ]
                        }
                    }
                },
                {
                    name: '南北向',
                    type: 'line',
                    smooth: true,
                    data: this.generateFlowData(),
                    lineStyle: {
                        color: '#00ff88',
                        width: 3
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(0, 255, 136, 0.3)' },
                                { offset: 1, color: 'rgba(0, 255, 136, 0.05)' }
                            ]
                        }
                    }
                },
                {
                    name: '预测值',
                    type: 'line',
                    smooth: true,
                    data: this.generatePredictionData(),
                    lineStyle: {
                        color: '#ffd700',
                        width: 2,
                        type: 'dashed'
                    },
                    symbol: 'none'
                }
            ]
        };

        chart.setOption(option);
        this.charts[containerId] = chart;
        return chart;
    }

    // 创建交通地图
    createTrafficMap(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return null;

        // 创建地图容器
        container.innerHTML = `
            <div id="${containerId}_map" style="width: 100%; height: 100%; position: relative;">
                <div class="map-overlay">
                    <div class="map-legend">
                        <h4>交通状况</h4>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #00ff88;"></span>
                            <span>畅通</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #ffd700;"></span>
                            <span>缓慢</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #ff6b6b;"></span>
                            <span>拥堵</span>
                        </div>
                    </div>
                </div>
                <svg id="${containerId}_svg" width="100%" height="100%">
                    <!-- 道路网络 -->
                    <g class="roads">
                        <!-- 主干道 -->
                        <line x1="10%" y1="50%" x2="90%" y2="50%" stroke="#00ff88" stroke-width="8" class="road main-road"/>
                        <line x1="50%" y1="10%" x2="50%" y2="90%" stroke="#ffd700" stroke-width="8" class="road main-road"/>
                        
                        <!-- 次干道 -->
                        <line x1="20%" y1="30%" x2="80%" y2="30%" stroke="#00ff88" stroke-width="6" class="road secondary-road"/>
                        <line x1="20%" y1="70%" x2="80%" y2="70%" stroke="#ff6b6b" stroke-width="6" class="road secondary-road"/>
                        <line x1="30%" y1="20%" x2="30%" y2="80%" stroke="#ffd700" stroke-width="6" class="road secondary-road"/>
                        <line x1="70%" y1="20%" x2="70%" y2="80%" stroke="#00ff88" stroke-width="6" class="road secondary-road"/>
                    </g>
                    
                    <!-- 交通节点 -->
                    <g class="intersections">
                        <circle cx="50%" cy="50%" r="15" fill="rgba(100, 181, 246, 0.8)" stroke="#64b5f6" stroke-width="2" class="intersection major"/>
                        <circle cx="30%" cy="30%" r="10" fill="rgba(0, 255, 136, 0.8)" stroke="#00ff88" stroke-width="2" class="intersection minor"/>
                        <circle cx="70%" cy="30%" r="10" fill="rgba(255, 215, 0, 0.8)" stroke="#ffd700" stroke-width="2" class="intersection minor"/>
                        <circle cx="30%" cy="70%" r="10" fill="rgba(255, 107, 107, 0.8)" stroke="#ff6b6b" stroke-width="2" class="intersection minor"/>
                        <circle cx="70%" cy="70%" r="10" fill="rgba(0, 255, 136, 0.8)" stroke="#00ff88" stroke-width="2" class="intersection minor"/>
                    </g>
                    
                    <!-- 车辆标记 -->
                    <g class="vehicles">
                        <!-- 动态车辆将通过JavaScript添加 -->
                    </g>
                    
                    <!-- 事件标记 -->
                    <g class="events">
                        <circle cx="60%" cy="40%" r="8" fill="#ff4444" stroke="#ffffff" stroke-width="2" class="event accident">
                            <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
                        </circle>
                        <text x="60%" y="35%" text-anchor="middle" fill="#ffffff" font-size="10">事故</text>
                    </g>
                </svg>
            </div>
        `;

        // 添加交互功能
        this.addMapInteractions(containerId);
        
        // 启动车辆动画
        this.startVehicleAnimation(containerId);

        return container;
    }

    // 创建速度分布雷达图
    createSpeedRadarChart(containerId, data) {
        const chartDom = document.getElementById(containerId);
        if (!chartDom) return null;

        const chart = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            title: {
                text: '速度分布雷达图',
                textStyle: {
                    color: '#64b5f6',
                    fontSize: 16
                },
                left: 'center'
            },
            tooltip: {
                trigger: 'item'
            },
            radar: {
                indicator: [
                    { name: '0-20km/h', max: 100 },
                    { name: '20-40km/h', max: 100 },
                    { name: '40-60km/h', max: 100 },
                    { name: '60-80km/h', max: 100 },
                    { name: '80-100km/h', max: 100 },
                    { name: '100km/h+', max: 100 }
                ],
                center: ['50%', '55%'],
                radius: '70%',
                axisName: {
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: 12
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(63, 81, 181, 0.4)'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(63, 81, 181, 0.2)'
                    }
                },
                splitArea: {
                    areaStyle: {
                        color: ['rgba(63, 81, 181, 0.1)', 'rgba(63, 81, 181, 0.05)']
                    }
                }
            },
            series: [{
                name: '速度分布',
                type: 'radar',
                data: [
                    {
                        value: [20, 45, 80, 60, 25, 10],
                        name: '当前时段',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#00d4ff',
                            width: 3
                        }
                    },
                    {
                        value: [15, 40, 75, 65, 30, 15],
                        name: '历史平均',
                        areaStyle: {
                            color: 'rgba(0, 255, 136, 0.2)'
                        },
                        lineStyle: {
                            color: '#00ff88',
                            width: 2,
                            type: 'dashed'
                        }
                    }
                ]
            }]
        };

        chart.setOption(option);
        this.charts[containerId] = chart;
        return chart;
    }

    // 创建拥堵预测图表
    createCongestionPredictionChart(containerId, data) {
        const chartDom = document.getElementById(containerId);
        if (!chartDom) return null;

        const chart = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            title: {
                text: '拥堵预测分析',
                textStyle: {
                    color: '#64b5f6',
                    fontSize: 16
                },
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['历史拥堵', '预测拥堵', '拥堵概率'],
                textStyle: {
                    color: 'rgba(255, 255, 255, 0.8)'
                },
                top: '8%'
            },
            grid: {
                left: '3%',
                right: '8%',
                bottom: '3%',
                top: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: this.generateTimeLabels(48), // 48小时预测
                axisLabel: {
                    color: 'rgba(255, 255, 255, 0.7)',
                    interval: 5
                },
                axisLine: {
                    lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '拥堵指数',
                    position: 'left',
                    axisLabel: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    axisLine: {
                        lineStyle: { color: 'rgba(63, 81, 181, 0.4)' }
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(63, 81, 181, 0.2)' }
                    }
                },
                {
                    type: 'value',
                    name: '概率 (%)',
                    position: 'right',
                    axisLabel: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        formatter: '{value}%'
                    },
                    axisLine: {
                        lineStyle: { color: 'rgba(255, 215, 0, 0.4)' }
                    }
                }
            ],
            series: [
                {
                    name: '历史拥堵',
                    type: 'line',
                    data: this.generateCongestionData(24),
                    lineStyle: {
                        color: '#64b5f6',
                        width: 2
                    },
                    symbol: 'circle',
                    symbolSize: 4
                },
                {
                    name: '预测拥堵',
                    type: 'line',
                    data: this.generateCongestionData(24, true),
                    lineStyle: {
                        color: '#ff6b6b',
                        width: 3,
                        type: 'dashed'
                    },
                    symbol: 'diamond',
                    symbolSize: 6
                },
                {
                    name: '拥堵概率',
                    type: 'bar',
                    yAxisIndex: 1,
                    data: this.generateProbabilityData(48),
                    itemStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(255, 215, 0, 0.8)' },
                                { offset: 1, color: 'rgba(255, 215, 0, 0.3)' }
                            ]
                        }
                    },
                    barWidth: '60%'
                }
            ]
        };

        chart.setOption(option);
        this.charts[containerId] = chart;
        return chart;
    }

    // 数据处理方法
    processHeatmapData(data) {
        const heatmapData = [];
        for (let hour = 0; hour < 24; hour++) {
            for (let road = 0; road < 6; road++) {
                const value = Math.floor(Math.random() * 400) + 100;
                heatmapData.push([hour, road, value]);
            }
        }
        return heatmapData;
    }

    generateTimeLabels(hours = 24) {
        const labels = [];
        const now = new Date();
        for (let i = 0; i < hours; i++) {
            const time = new Date(now.getTime() + i * 60 * 60 * 1000);
            labels.push(time.getHours().toString().padStart(2, '0') + ':00');
        }
        return labels;
    }

    generateFlowData() {
        return Array.from({length: 24}, () => Math.floor(Math.random() * 300) + 200);
    }

    generatePredictionData() {
        return Array.from({length: 24}, () => Math.floor(Math.random() * 250) + 180);
    }

    generateCongestionData(length, isPrediction = false) {
        const baseValue = isPrediction ? 0.6 : 0.5;
        return Array.from({length}, () => 
            Math.max(0, Math.min(1, baseValue + (Math.random() - 0.5) * 0.4))
        );
    }

    generateProbabilityData(length) {
        return Array.from({length}, () => Math.floor(Math.random() * 80) + 10);
    }

    // 地图交互功能
    addMapInteractions(containerId) {
        const svg = document.getElementById(`${containerId}_svg`);
        if (!svg) return;

        // 添加道路点击事件
        svg.querySelectorAll('.road').forEach(road => {
            road.addEventListener('click', (e) => {
                this.showRoadInfo(e.target);
            });
            
            road.addEventListener('mouseover', (e) => {
                e.target.style.strokeWidth = (parseInt(e.target.style.strokeWidth) + 2) + 'px';
            });
            
            road.addEventListener('mouseout', (e) => {
                e.target.style.strokeWidth = (parseInt(e.target.style.strokeWidth) - 2) + 'px';
            });
        });

        // 添加路口点击事件
        svg.querySelectorAll('.intersection').forEach(intersection => {
            intersection.addEventListener('click', (e) => {
                this.showIntersectionInfo(e.target);
            });
        });
    }

    // 启动车辆动画
    startVehicleAnimation(containerId) {
        const svg = document.getElementById(`${containerId}_svg`);
        if (!svg) return;

        const vehiclesGroup = svg.querySelector('.vehicles');
        
        // 创建移动的车辆
        setInterval(() => {
            this.addMovingVehicle(vehiclesGroup);
        }, 2000);
    }

    addMovingVehicle(container) {
        const vehicle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        vehicle.setAttribute('r', '3');
        vehicle.setAttribute('fill', '#00d4ff');
        vehicle.setAttribute('stroke', '#ffffff');
        vehicle.setAttribute('stroke-width', '1');
        
        // 随机选择路径
        const paths = [
            { start: '10%', end: '90%', axis: 'cx', fixed: 'cy', fixedValue: '50%' },
            { start: '90%', end: '10%', axis: 'cx', fixed: 'cy', fixedValue: '50%' },
            { start: '10%', end: '90%', axis: 'cy', fixed: 'cx', fixedValue: '50%' },
            { start: '90%', end: '10%', axis: 'cy', fixed: 'cx', fixedValue: '50%' }
        ];
        
        const path = paths[Math.floor(Math.random() * paths.length)];
        
        vehicle.setAttribute(path.fixed, path.fixedValue);
        vehicle.setAttribute(path.axis, path.start);
        
        container.appendChild(vehicle);
        
        // 动画移动
        const animation = document.createElementNS('http://www.w3.org/2000/svg', 'animate');
        animation.setAttribute('attributeName', path.axis);
        animation.setAttribute('values', `${path.start};${path.end}`);
        animation.setAttribute('dur', '5s');
        animation.setAttribute('repeatCount', '1');
        
        vehicle.appendChild(animation);
        
        // 动画结束后移除
        setTimeout(() => {
            if (vehicle.parentNode) {
                vehicle.parentNode.removeChild(vehicle);
            }
        }, 5000);
    }

    showRoadInfo(roadElement) {
        const roadClass = roadElement.classList.contains('main-road') ? '主干道' : '次干道';
        const status = roadElement.getAttribute('stroke') === '#00ff88' ? '畅通' : 
                     roadElement.getAttribute('stroke') === '#ffd700' ? '缓慢' : '拥堵';
        
        alert(`${roadClass}\n状态: ${status}\n平均速度: ${Math.floor(Math.random() * 40) + 30}km/h`);
    }

    showIntersectionInfo(intersectionElement) {
        const type = intersectionElement.classList.contains('major') ? '主要路口' : '次要路口';
        const flow = Math.floor(Math.random() * 200) + 100;
        
        alert(`${type}\n当前流量: ${flow}辆/小时\n信号灯状态: 正常`);
    }

    // 更新图表数据
    updateChart(chartId, newData) {
        if (this.charts[chartId]) {
            this.charts[chartId].setOption(newData);
        }
    }

    // 响应式处理
    handleResize() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    // 销毁图表
    dispose() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.dispose) {
                chart.dispose();
            }
        });
        this.charts = {};
    }
}

// 导出高级图表类
window.AdvancedCharts = AdvancedCharts;
