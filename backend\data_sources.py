# -*- coding: utf-8 -*-
"""
多源数据采集模块
支持地磁线圈、摄像头、手机信令等多种数据源
"""

import json
import time
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import asyncio
import aiohttp
import struct
import socket
from abc import ABC, abstractmethod

from models import db, TrafficFlow, VehicleSpeed, TrafficEvent, DeviceStatus

logger = logging.getLogger(__name__)

class DataSource(ABC):
    """数据源基类"""
    
    def __init__(self, source_id: str, source_name: str, config: Dict):
        self.source_id = source_id
        self.source_name = source_name
        self.config = config
        self.is_active = False
        self.last_update = None
        
    @abstractmethod
    async def collect_data(self) -> List[Dict]:
        """采集数据的抽象方法"""
        pass
    
    @abstractmethod
    def validate_data(self, data: Dict) -> bool:
        """验证数据格式的抽象方法"""
        pass
    
    def normalize_data(self, raw_data: Dict) -> Dict:
        """标准化数据格式"""
        return {
            'source_id': self.source_id,
            'source_type': self.__class__.__name__,
            'timestamp': datetime.utcnow(),
            'raw_data': raw_data,
            'processed_data': self._process_raw_data(raw_data)
        }
    
    def _process_raw_data(self, raw_data: Dict) -> Dict:
        """处理原始数据，子类可重写"""
        return raw_data

class MagneticLoopDetector(DataSource):
    """地磁线圈检测器数据源"""
    
    def __init__(self, source_id: str, config: Dict):
        super().__init__(source_id, "地磁线圈检测器", config)
        self.loop_positions = config.get('positions', [])
        self.sensitivity = config.get('sensitivity', 0.8)
        
    async def collect_data(self) -> List[Dict]:
        """采集地磁线圈数据"""
        try:
            collected_data = []
            
            for position in self.loop_positions:
                # 模拟地磁线圈数据采集
                loop_data = await self._read_magnetic_loop(position)
                if self.validate_data(loop_data):
                    normalized_data = self.normalize_data(loop_data)
                    collected_data.append(normalized_data)
                    
            logger.info(f"地磁线圈采集到 {len(collected_data)} 条数据")
            return collected_data
            
        except Exception as e:
            logger.error(f"地磁线圈数据采集失败: {e}")
            return []
    
    async def _read_magnetic_loop(self, position: Dict) -> Dict:
        """读取单个地磁线圈数据"""
        # 模拟地磁线圈检测数据
        vehicle_detected = random.random() < 0.3  # 30%概率检测到车辆
        
        if vehicle_detected:
            # 模拟车辆通过时的地磁信号变化
            magnetic_strength = random.uniform(0.2, 1.0)
            vehicle_length = random.uniform(3.5, 12.0)  # 车长3.5-12米
            speed = random.uniform(20, 100)  # 速度20-100km/h
            
            # 根据磁场强度和车长估算车型
            if vehicle_length < 5:
                vehicle_type = "小型车"
            elif vehicle_length < 8:
                vehicle_type = "中型车"
            else:
                vehicle_type = "大型车"
                
            return {
                'position_id': position['id'],
                'position_name': position['name'],
                'coordinates': position['coordinates'],
                'detection_time': datetime.utcnow(),
                'magnetic_strength': magnetic_strength,
                'vehicle_detected': True,
                'vehicle_length': vehicle_length,
                'estimated_speed': speed,
                'vehicle_type': vehicle_type,
                'signal_quality': random.uniform(0.7, 1.0)
            }
        else:
            return {
                'position_id': position['id'],
                'position_name': position['name'],
                'coordinates': position['coordinates'],
                'detection_time': datetime.utcnow(),
                'magnetic_strength': random.uniform(0.0, 0.1),
                'vehicle_detected': False,
                'signal_quality': random.uniform(0.8, 1.0)
            }
    
    def validate_data(self, data: Dict) -> bool:
        """验证地磁线圈数据"""
        required_fields = ['position_id', 'detection_time', 'magnetic_strength', 'vehicle_detected']
        return all(field in data for field in required_fields)
    
    def _process_raw_data(self, raw_data: Dict) -> Dict:
        """处理地磁线圈原始数据"""
        processed = {
            'location_id': raw_data['position_id'],
            'location_name': raw_data['position_name'],
            'record_time': raw_data['detection_time'],
            'data_type': 'magnetic_loop'
        }
        
        if raw_data['vehicle_detected']:
            processed.update({
                'flow_count': 1,
                'vehicle_type': raw_data.get('vehicle_type', 'unknown'),
                'speed': raw_data.get('estimated_speed', 0),
                'vehicle_length': raw_data.get('vehicle_length', 0)
            })
        else:
            processed.update({
                'flow_count': 0,
                'vehicle_type': None,
                'speed': 0
            })
            
        return processed

class CameraDetector(DataSource):
    """摄像头检测器数据源"""
    
    def __init__(self, source_id: str, config: Dict):
        super().__init__(source_id, "摄像头检测器", config)
        self.camera_urls = config.get('camera_urls', [])
        self.detection_zones = config.get('detection_zones', [])
        
    async def collect_data(self) -> List[Dict]:
        """采集摄像头数据"""
        try:
            collected_data = []
            
            for camera_config in self.camera_urls:
                camera_data = await self._process_camera_feed(camera_config)
                if self.validate_data(camera_data):
                    normalized_data = self.normalize_data(camera_data)
                    collected_data.append(normalized_data)
                    
            logger.info(f"摄像头采集到 {len(collected_data)} 条数据")
            return collected_data
            
        except Exception as e:
            logger.error(f"摄像头数据采集失败: {e}")
            return []
    
    async def _process_camera_feed(self, camera_config: Dict) -> Dict:
        """处理摄像头视频流"""
        # 模拟视频分析结果
        camera_id = camera_config['id']
        camera_name = camera_config['name']
        
        # 模拟车辆检测结果
        detected_vehicles = []
        vehicle_count = random.randint(0, 8)
        
        for i in range(vehicle_count):
            vehicle = {
                'vehicle_id': f"V_{camera_id}_{i}_{int(time.time())}",
                'bbox': {  # 边界框坐标
                    'x': random.randint(0, 1920),
                    'y': random.randint(0, 1080),
                    'width': random.randint(50, 200),
                    'height': random.randint(30, 100)
                },
                'confidence': random.uniform(0.7, 0.99),
                'vehicle_type': random.choice(['小型车', '中型车', '大型车', '货车', '客车']),
                'color': random.choice(['白色', '黑色', '银色', '红色', '蓝色']),
                'speed': random.uniform(10, 80),
                'direction': random.choice(['东向西', '西向东', '南向北', '北向南'])
            }
            detected_vehicles.append(vehicle)
        
        # 模拟交通事件检测
        events = []
        if random.random() < 0.1:  # 10%概率检测到事件
            event_type = random.choice(['违法停车', '逆向行驶', '变道违法', '超速'])
            events.append({
                'event_type': event_type,
                'confidence': random.uniform(0.6, 0.9),
                'location': camera_config.get('location', '未知位置'),
                'description': f"摄像头检测到{event_type}"
            })
        
        return {
            'camera_id': camera_id,
            'camera_name': camera_name,
            'location': camera_config.get('location', '未知位置'),
            'coordinates': camera_config.get('coordinates', {}),
            'capture_time': datetime.utcnow(),
            'detected_vehicles': detected_vehicles,
            'vehicle_count': len(detected_vehicles),
            'events': events,
            'image_quality': random.uniform(0.8, 1.0),
            'weather_condition': random.choice(['晴天', '阴天', '雨天', '雾天'])
        }
    
    def validate_data(self, data: Dict) -> bool:
        """验证摄像头数据"""
        required_fields = ['camera_id', 'capture_time', 'detected_vehicles', 'vehicle_count']
        return all(field in data for field in required_fields)
    
    def _process_raw_data(self, raw_data: Dict) -> Dict:
        """处理摄像头原始数据"""
        return {
            'location_id': raw_data['camera_id'],
            'location_name': raw_data['camera_name'],
            'record_time': raw_data['capture_time'],
            'flow_count': raw_data['vehicle_count'],
            'data_type': 'camera',
            'detected_vehicles': raw_data['detected_vehicles'],
            'events': raw_data['events'],
            'weather_condition': raw_data.get('weather_condition')
        }

class MobileSignalDetector(DataSource):
    """手机信令数据源"""
    
    def __init__(self, source_id: str, config: Dict):
        super().__init__(source_id, "手机信令检测器", config)
        self.base_stations = config.get('base_stations', [])
        self.signal_threshold = config.get('signal_threshold', -80)  # dBm
        
    async def collect_data(self) -> List[Dict]:
        """采集手机信令数据"""
        try:
            collected_data = []
            
            for base_station in self.base_stations:
                signal_data = await self._collect_mobile_signals(base_station)
                if self.validate_data(signal_data):
                    normalized_data = self.normalize_data(signal_data)
                    collected_data.append(normalized_data)
                    
            logger.info(f"手机信令采集到 {len(collected_data)} 条数据")
            return collected_data
            
        except Exception as e:
            logger.error(f"手机信令数据采集失败: {e}")
            return []
    
    async def _collect_mobile_signals(self, base_station: Dict) -> Dict:
        """采集基站手机信令数据"""
        station_id = base_station['id']
        station_name = base_station['name']
        
        # 模拟手机信令数据
        connected_devices = random.randint(50, 500)  # 连接设备数
        
        # 模拟移动轨迹分析
        movement_patterns = []
        for i in range(random.randint(10, 50)):
            pattern = {
                'device_id': f"DEV_{station_id}_{i}",  # 匿名化设备ID
                'signal_strength': random.uniform(-100, -30),  # dBm
                'duration': random.randint(30, 3600),  # 停留时间（秒）
                'movement_type': random.choice(['静止', '步行', '骑行', '驾车', '公交']),
                'estimated_speed': self._estimate_speed_from_signal(),
                'previous_station': random.choice([None] + [bs['id'] for bs in self.base_stations]),
                'next_station': random.choice([None] + [bs['id'] for bs in self.base_stations])
            }
            movement_patterns.append(pattern)
        
        # 统计交通模式
        traffic_modes = {
            '步行': len([p for p in movement_patterns if p['movement_type'] == '步行']),
            '骑行': len([p for p in movement_patterns if p['movement_type'] == '骑行']),
            '驾车': len([p for p in movement_patterns if p['movement_type'] == '驾车']),
            '公交': len([p for p in movement_patterns if p['movement_type'] == '公交'])
        }
        
        return {
            'station_id': station_id,
            'station_name': station_name,
            'location': base_station.get('location', '未知位置'),
            'coordinates': base_station.get('coordinates', {}),
            'collection_time': datetime.utcnow(),
            'connected_devices': connected_devices,
            'movement_patterns': movement_patterns,
            'traffic_modes': traffic_modes,
            'coverage_radius': base_station.get('coverage_radius', 1000),  # 覆盖半径（米）
            'signal_quality': random.uniform(0.7, 1.0)
        }
    
    def _estimate_speed_from_signal(self) -> float:
        """根据信号变化估算移动速度"""
        # 模拟基于信号强度变化的速度估算
        speed_categories = {
            '静止': random.uniform(0, 2),
            '步行': random.uniform(2, 8),
            '骑行': random.uniform(8, 25),
            '驾车': random.uniform(25, 80),
            '公交': random.uniform(15, 60)
        }
        category = random.choice(list(speed_categories.keys()))
        return speed_categories[category]
    
    def validate_data(self, data: Dict) -> bool:
        """验证手机信令数据"""
        required_fields = ['station_id', 'collection_time', 'connected_devices', 'movement_patterns']
        return all(field in data for field in required_fields)
    
    def _process_raw_data(self, raw_data: Dict) -> Dict:
        """处理手机信令原始数据"""
        # 计算交通流量（基于移动设备数量和移动模式）
        driving_devices = len([p for p in raw_data['movement_patterns'] if p['movement_type'] == '驾车'])
        
        return {
            'location_id': raw_data['station_id'],
            'location_name': raw_data['station_name'],
            'record_time': raw_data['collection_time'],
            'flow_count': driving_devices,  # 驾车设备数作为车流量指标
            'data_type': 'mobile_signal',
            'connected_devices': raw_data['connected_devices'],
            'traffic_modes': raw_data['traffic_modes'],
            'coverage_area': raw_data.get('coverage_radius', 1000)
        }

class DataIntegrator:
    """数据整合器"""
    
    def __init__(self):
        self.data_sources = {}
        self.integration_rules = {}
        self.quality_thresholds = {
            'magnetic_loop': 0.7,
            'camera': 0.8,
            'mobile_signal': 0.6
        }
    
    def register_data_source(self, source: DataSource):
        """注册数据源"""
        self.data_sources[source.source_id] = source
        logger.info(f"注册数据源: {source.source_name} ({source.source_id})")
    
    async def collect_all_data(self) -> List[Dict]:
        """采集所有数据源的数据"""
        all_data = []
        
        for source_id, source in self.data_sources.items():
            try:
                source_data = await source.collect_data()
                all_data.extend(source_data)
            except Exception as e:
                logger.error(f"数据源 {source_id} 采集失败: {e}")
        
        return all_data
    
    def integrate_data(self, raw_data_list: List[Dict]) -> List[Dict]:
        """整合多源数据"""
        integrated_data = []
        
        # 按位置和时间窗口分组数据
        grouped_data = self._group_data_by_location_time(raw_data_list)
        
        for group_key, group_data in grouped_data.items():
            integrated_record = self._merge_group_data(group_data)
            if integrated_record:
                integrated_data.append(integrated_record)
        
        logger.info(f"数据整合完成，生成 {len(integrated_data)} 条整合记录")
        return integrated_data
    
    def _group_data_by_location_time(self, data_list: List[Dict]) -> Dict:
        """按位置和时间窗口分组数据"""
        groups = {}
        time_window = timedelta(minutes=5)  # 5分钟时间窗口
        
        for data in data_list:
            processed_data = data.get('processed_data', {})
            location_id = processed_data.get('location_id', 'unknown')
            record_time = processed_data.get('record_time', datetime.utcnow())
            
            # 计算时间窗口
            time_slot = record_time.replace(second=0, microsecond=0)
            time_slot = time_slot.replace(minute=(time_slot.minute // 5) * 5)
            
            group_key = f"{location_id}_{time_slot.isoformat()}"
            
            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append(data)
        
        return groups
    
    def _merge_group_data(self, group_data: List[Dict]) -> Optional[Dict]:
        """合并同组数据"""
        if not group_data:
            return None
        
        # 提取基础信息
        first_data = group_data[0]['processed_data']
        merged_data = {
            'location_id': first_data['location_id'],
            'location_name': first_data.get('location_name', '未知位置'),
            'record_time': first_data['record_time'],
            'data_sources': [],
            'integrated_metrics': {}
        }
        
        # 合并各数据源的信息
        total_flow = 0
        speed_values = []
        confidence_scores = []
        
        for data in group_data:
            source_type = data['source_type']
            processed_data = data['processed_data']
            
            merged_data['data_sources'].append({
                'source_type': source_type,
                'source_id': data['source_id'],
                'confidence': self._calculate_confidence(data)
            })
            
            # 累计流量
            flow_count = processed_data.get('flow_count', 0)
            if flow_count > 0:
                total_flow += flow_count
            
            # 收集速度数据
            speed = processed_data.get('speed', 0)
            if speed > 0:
                speed_values.append(speed)
            
            # 收集置信度
            confidence = self._calculate_confidence(data)
            confidence_scores.append(confidence)
        
        # 计算整合指标
        merged_data['integrated_metrics'] = {
            'total_flow': total_flow,
            'average_speed': sum(speed_values) / len(speed_values) if speed_values else 0,
            'data_quality': sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
            'source_count': len(group_data),
            'integration_time': datetime.utcnow()
        }
        
        return merged_data
    
    def _calculate_confidence(self, data: Dict) -> float:
        """计算数据置信度"""
        source_type = data['source_type']
        raw_data = data.get('raw_data', {})
        
        # 基础置信度
        base_confidence = 0.5
        
        # 根据数据源类型调整
        if source_type == 'MagneticLoopDetector':
            signal_quality = raw_data.get('signal_quality', 0.5)
            base_confidence = signal_quality * 0.9
        elif source_type == 'CameraDetector':
            image_quality = raw_data.get('image_quality', 0.5)
            weather_factor = 0.9 if raw_data.get('weather_condition') == '晴天' else 0.7
            base_confidence = image_quality * weather_factor
        elif source_type == 'MobileSignalDetector':
            signal_quality = raw_data.get('signal_quality', 0.5)
            device_count = raw_data.get('connected_devices', 0)
            count_factor = min(1.0, device_count / 100)  # 设备数量越多置信度越高
            base_confidence = signal_quality * count_factor
        
        return min(1.0, max(0.0, base_confidence))
    
    def validate_integrated_data(self, integrated_data: List[Dict]) -> List[Dict]:
        """验证整合后的数据质量"""
        validated_data = []
        
        for data in integrated_data:
            quality_score = data['integrated_metrics']['data_quality']
            source_count = data['integrated_metrics']['source_count']
            
            # 质量检查规则
            if quality_score >= 0.6 and source_count >= 1:
                validated_data.append(data)
            else:
                logger.warning(f"数据质量不达标，丢弃数据: {data['location_id']}")
        
        logger.info(f"数据验证完成，保留 {len(validated_data)}/{len(integrated_data)} 条数据")
        return validated_data

# 创建全局数据整合器实例
data_integrator = DataIntegrator()
