<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧城市交通流量可视化数据分析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0a1428 0%, #1e3a5f 50%, #0a1428 100%);
            color: #ffffff;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
        }

        .title {
            font-size: 48px;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            margin-bottom: 20px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        .subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 50px;
            letter-spacing: 2px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 40px;
        }

        .dashboard-card {
            background: linear-gradient(145deg, rgba(0, 50, 100, 0.4), rgba(0, 100, 150, 0.2));
            border: 1px solid rgba(0, 150, 255, 0.3);
            border-radius: 20px;
            padding: 40px 30px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .dashboard-card:hover::before {
            left: 100%;
        }

        .dashboard-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 150, 255, 0.3);
            border-color: #00d4ff;
        }

        .card-icon {
            font-size: 60px;
            margin-bottom: 20px;
            display: block;
        }

        .card-title {
            font-size: 24px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 15px;
        }

        .card-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-features {
            list-style: none;
            text-align: left;
        }

        .card-features li {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 8px;
            padding-left: 15px;
            position: relative;
        }

        .card-features li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #00ff88;
        }

        .api-info {
            margin-top: 40px;
            padding: 20px;
            background: rgba(0, 50, 100, 0.3);
            border-radius: 12px;
            border: 1px solid rgba(0, 150, 255, 0.2);
        }

        .api-title {
            font-size: 18px;
            color: #00ff88;
            margin-bottom: 15px;
        }

        .api-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .api-link {
            color: #64b5f6;
            text-decoration: none;
            font-size: 12px;
            padding: 8px 15px;
            border: 1px solid rgba(100, 181, 246, 0.3);
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .api-link:hover {
            background: rgba(100, 181, 246, 0.2);
            transform: translateY(-2px);
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(0, 212, 255, 0.5); }
            to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8), 0 0 40px rgba(0, 212, 255, 0.3); }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .title {
                font-size: 36px;
            }
            
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">智慧城市交通流量可视化数据分析系统</h1>
        <p class="subtitle">Smart City Traffic Flow Visualization and Data Analysis System</p>
        
        <div class="dashboard-grid">
            <a href="index.html" class="dashboard-card">
                <span class="card-icon">🛣️</span>
                <h2 class="card-title">智慧交通运营管理平台</h2>
                <p class="card-description">
                    综合性交通监控大屏，提供全方位的交通流量监控和数据分析功能
                </p>
                <ul class="card-features">
                    <li>实时车流量监控</li>
                    <li>3D道路场景展示</li>
                    <li>车辆轨迹追踪</li>
                    <li>智能预警系统</li>
                    <li>多维度数据统计</li>
                </ul>
            </a>
            
            <a href="intersection.html" class="dashboard-card">
                <span class="card-icon">🚦</span>
                <h2 class="card-title">交通监控平台</h2>
                <p class="card-description">
                    专注于路口监控的精细化管理平台，提供路口级别的详细监控和控制
                </p>
                <ul class="card-features">
                    <li>路口3D可视化</li>
                    <li>信号灯智能控制</li>
                    <li>违法行为检测</li>
                    <li>事件实时处理</li>
                    <li>设备状态监控</li>
                </ul>
            </a>
        </div>
        
        <div class="api-info">
            <h3 class="api-title">🔗 API接口服务</h3>
            <div class="api-links">
                <a href="http://localhost:5000" class="api-link" target="_blank">API首页</a>
                <a href="http://localhost:5000/api/traffic/flow" class="api-link" target="_blank">车流量数据</a>
                <a href="http://localhost:5000/api/traffic/realtime" class="api-link" target="_blank">实时数据</a>
                <a href="http://localhost:5000/api/traffic/events" class="api-link" target="_blank">事件列表</a>
            </div>
        </div>
    </div>

    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
            
            // API信息动画
            const apiInfo = document.querySelector('.api-info');
            apiInfo.style.opacity = '0';
            apiInfo.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                apiInfo.style.transition = 'all 0.6s ease';
                apiInfo.style.opacity = '1';
                apiInfo.style.transform = 'translateY(0)';
            }, 800);
        });

        // 检查API服务状态
        async function checkApiStatus() {
            try {
                const response = await fetch('http://localhost:5000');
                if (response.ok) {
                    console.log('✅ 后端API服务运行正常');
                } else {
                    console.warn('⚠️ 后端API服务响应异常');
                }
            } catch (error) {
                console.warn('⚠️ 无法连接到后端API服务，请确保后端服务已启动');
            }
        }

        // 页面加载时检查API状态
        checkApiStatus();
    </script>
</body>
</html>
