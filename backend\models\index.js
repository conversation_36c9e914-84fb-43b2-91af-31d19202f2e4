/**
 * 数据库模型定义
 * Database Models Definition
 */

const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');
const logger = require('../utils/logger');

// 数据库配置
const sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: path.join(__dirname, '../database/traffic.db'),
    logging: (msg) => logger.debug(msg),
    define: {
        timestamps: true,
        underscored: false,
        freezeTableName: true
    },
    pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
    }
});

// 交通流量模型
const TrafficFlow = sequelize.define('TrafficFlow', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    locationId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'location_id'
    },
    locationName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'location_name'
    },
    flowCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        field: 'flow_count'
    },
    vehicleType: {
        type: DataTypes.STRING(20),
        field: 'vehicle_type'
    },
    recordTime: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'record_time'
    },
    dataSource: {
        type: DataTypes.STRING(30),
        allowNull: false,
        field: 'data_source'
    },
    rawData: {
        type: DataTypes.TEXT,
        field: 'raw_data'
    },
    confidence: {
        type: DataTypes.FLOAT,
        defaultValue: 1.0
    }
}, {
    tableName: 'traffic_flows',
    indexes: [
        { fields: ['location_id'] },
        { fields: ['record_time'] },
        { fields: ['data_source'] },
        { fields: ['location_id', 'record_time'] }
    ]
});

// 车辆速度模型
const VehicleSpeed = sequelize.define('VehicleSpeed', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    locationId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'location_id'
    },
    speed: {
        type: DataTypes.FLOAT,
        allowNull: false
    },
    vehicleType: {
        type: DataTypes.STRING(20),
        field: 'vehicle_type'
    },
    recordTime: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'record_time'
    },
    dataSource: {
        type: DataTypes.STRING(30),
        allowNull: false,
        field: 'data_source'
    },
    direction: {
        type: DataTypes.STRING(20)
    },
    laneNumber: {
        type: DataTypes.INTEGER,
        field: 'lane_number'
    }
}, {
    tableName: 'vehicle_speeds',
    indexes: [
        { fields: ['location_id'] },
        { fields: ['record_time'] },
        { fields: ['vehicle_type'] }
    ]
});

// 交通事件模型
const TrafficEvent = sequelize.define('TrafficEvent', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    locationId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'location_id'
    },
    locationName: {
        type: DataTypes.STRING(100),
        field: 'location_name'
    },
    eventType: {
        type: DataTypes.STRING(30),
        allowNull: false,
        field: 'event_type'
    },
    description: {
        type: DataTypes.TEXT
    },
    severity: {
        type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
        defaultValue: 'medium'
    },
    status: {
        type: DataTypes.ENUM('active', 'resolved', 'investigating'),
        defaultValue: 'active'
    },
    startTime: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'start_time'
    },
    endTime: {
        type: DataTypes.DATE,
        field: 'end_time'
    },
    dataSource: {
        type: DataTypes.STRING(30),
        allowNull: false,
        field: 'data_source'
    },
    metadata: {
        type: DataTypes.TEXT
    }
}, {
    tableName: 'traffic_events',
    indexes: [
        { fields: ['location_id'] },
        { fields: ['event_type'] },
        { fields: ['status'] },
        { fields: ['start_time'] }
    ]
});

// 设备状态模型
const DeviceStatus = sequelize.define('DeviceStatus', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    deviceId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        field: 'device_id'
    },
    deviceName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'device_name'
    },
    deviceType: {
        type: DataTypes.STRING(30),
        allowNull: false,
        field: 'device_type'
    },
    status: {
        type: DataTypes.ENUM('online', 'offline', 'maintenance', 'error'),
        defaultValue: 'offline'
    },
    location: {
        type: DataTypes.STRING(100)
    },
    coordinates: {
        type: DataTypes.TEXT
    },
    lastUpdate: {
        type: DataTypes.DATE,
        field: 'last_update'
    },
    configuration: {
        type: DataTypes.TEXT
    },
    errorMessage: {
        type: DataTypes.TEXT,
        field: 'error_message'
    }
}, {
    tableName: 'device_status',
    indexes: [
        { fields: ['device_id'] },
        { fields: ['device_type'] },
        { fields: ['status'] }
    ]
});

// 违法记录模型
const ViolationRecord = sequelize.define('ViolationRecord', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    locationId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'location_id'
    },
    violationType: {
        type: DataTypes.STRING(30),
        allowNull: false,
        field: 'violation_type'
    },
    vehicleType: {
        type: DataTypes.STRING(20),
        field: 'vehicle_type'
    },
    plateNumber: {
        type: DataTypes.STRING(20),
        field: 'plate_number'
    },
    recordTime: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'record_time'
    },
    confidence: {
        type: DataTypes.FLOAT,
        defaultValue: 1.0
    },
    imageUrl: {
        type: DataTypes.STRING(200),
        field: 'image_url'
    },
    processed: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    }
}, {
    tableName: 'violation_records',
    indexes: [
        { fields: ['location_id'] },
        { fields: ['violation_type'] },
        { fields: ['record_time'] },
        { fields: ['processed'] }
    ]
});

// 路口状态模型
const IntersectionStatus = sequelize.define('IntersectionStatus', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    intersectionId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        field: 'intersection_id'
    },
    intersectionName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'intersection_name'
    },
    signalStatus: {
        type: DataTypes.TEXT,
        field: 'signal_status'
    },
    trafficFlow: {
        type: DataTypes.TEXT,
        field: 'traffic_flow'
    },
    queueLength: {
        type: DataTypes.TEXT,
        field: 'queue_length'
    },
    averageSpeed: {
        type: DataTypes.FLOAT,
        field: 'average_speed'
    },
    incidentCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        field: 'incident_count'
    },
    lastUpdate: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        field: 'last_update'
    }
}, {
    tableName: 'intersection_status',
    indexes: [
        { fields: ['intersection_id'] },
        { fields: ['last_update'] }
    ]
});

// 分析结果模型
const AnalysisResult = sequelize.define('AnalysisResult', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    analysisType: {
        type: DataTypes.STRING(30),
        allowNull: false,
        field: 'analysis_type'
    },
    timeRange: {
        type: DataTypes.STRING(50),
        field: 'time_range'
    },
    parameters: {
        type: DataTypes.TEXT
    },
    results: {
        type: DataTypes.TEXT,
        allowNull: false
    },
    confidence: {
        type: DataTypes.FLOAT
    },
    executionTime: {
        type: DataTypes.INTEGER,
        field: 'execution_time'
    },
    status: {
        type: DataTypes.ENUM('pending', 'running', 'completed', 'failed'),
        defaultValue: 'pending'
    }
}, {
    tableName: 'analysis_results',
    indexes: [
        { fields: ['analysis_type'] },
        { fields: ['status'] },
        { fields: ['createdAt'] }
    ]
});

// 定义关联关系
TrafficFlow.belongsTo(DeviceStatus, { foreignKey: 'locationId', targetKey: 'deviceId', as: 'device' });
VehicleSpeed.belongsTo(DeviceStatus, { foreignKey: 'locationId', targetKey: 'deviceId', as: 'device' });
TrafficEvent.belongsTo(DeviceStatus, { foreignKey: 'locationId', targetKey: 'deviceId', as: 'device' });

DeviceStatus.hasMany(TrafficFlow, { foreignKey: 'locationId', sourceKey: 'deviceId', as: 'flows' });
DeviceStatus.hasMany(VehicleSpeed, { foreignKey: 'locationId', sourceKey: 'deviceId', as: 'speeds' });
DeviceStatus.hasMany(TrafficEvent, { foreignKey: 'locationId', sourceKey: 'deviceId', as: 'events' });

module.exports = {
    sequelize,
    TrafficFlow,
    VehicleSpeed,
    TrafficEvent,
    DeviceStatus,
    ViolationRecord,
    IntersectionStatus,
    AnalysisResult
};