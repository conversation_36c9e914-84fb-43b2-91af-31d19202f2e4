@echo off
chcp 65001
echo ========================================
echo 智慧城市交通流量可视化系统 - 简化启动
echo ========================================
echo.

echo 🔍 检测Python环境...

REM 尝试不同的Python命令
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    echo ✅ 找到Python命令: python
    goto :start_services
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    echo ✅ 找到Python命令: python3
    goto :start_services
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    echo ✅ 找到Python命令: py
    goto :start_services
)

echo ❌ 未找到Python，请先安装Python
echo.
echo 📥 Python安装步骤:
echo 1. 访问 https://www.python.org/downloads/
echo 2. 下载Python 3.9+版本
echo 3. 安装时勾选 "Add Python to PATH"
echo 4. 重启命令提示符后再运行此脚本
echo.
pause
exit /b 1

:start_services
echo.
echo 🚀 启动前端服务（无需后端）...
cd frontend

echo.
echo 📱 启动Web服务器...
start "智慧交通前端" cmd /k "%PYTHON_CMD% -m http.server 8080"

echo.
echo ⏳ 等待服务启动...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo 🎉 前端服务启动成功！
echo ========================================
echo.
echo 📱 访问地址:
echo    导航页面: http://localhost:8080/navigation.html
echo    主界面: http://localhost:8080/index.html  
echo    路口监控: http://localhost:8080/intersection.html
echo.
echo 💡 说明:
echo - 前端使用模拟数据，无需数据库
echo - 所有功能都可正常使用
echo - 关闭命令窗口即可停止服务
echo.
echo ========================================

REM 自动打开浏览器
echo 🌐 正在打开浏览器...
timeout /t 2 /nobreak >nul
start http://localhost:8080/navigation.html

echo.
echo 按任意键退出...
pause >nul
