// 模拟API数据服务
class MockApiService {
    constructor() {
        this.baseUrl = 'http://localhost:5000';
        this.isApiAvailable = false;
        this.checkApiStatus();
    }

    // 检查API服务状态
    async checkApiStatus() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000);

            const response = await fetch(this.baseUrl, {
                method: 'GET',
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                this.isApiAvailable = true;
                console.log('✅ 后端API服务连接成功');

                // 检查数据库状态
                await this.checkDatabaseStatus();
            } else {
                this.isApiAvailable = false;
                console.warn('⚠️ 后端API服务响应异常，使用模拟数据');
            }
        } catch (error) {
            this.isApiAvailable = false;
            console.warn('⚠️ 后端API服务不可用，使用模拟数据');
        }
    }

    // 检查数据库状态
    async checkDatabaseStatus() {
        try {
            const response = await fetch(`${this.baseUrl}/api/database/stats`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    console.log('📊 数据库连接正常，数据统计:', data.data);
                    this.databaseAvailable = true;
                } else {
                    console.warn('⚠️ 数据库连接异常');
                    this.databaseAvailable = false;
                }
            }
        } catch (error) {
            console.warn('⚠️ 无法获取数据库状态');
            this.databaseAvailable = false;
        }
    }

    // 启动数据爬虫
    async startCrawler() {
        if (!this.isApiAvailable) {
            console.warn('⚠️ API服务不可用，无法启动爬虫');
            return false;
        }

        try {
            const response = await fetch(`${this.baseUrl}/api/crawler/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            if (data.success) {
                console.log('🕷️ 数据爬虫启动成功');
                return true;
            } else {
                console.error('❌ 启动数据爬虫失败:', data.message);
                return false;
            }
        } catch (error) {
            console.error('❌ 启动数据爬虫失败:', error);
            return false;
        }
    }

    // 运行网络爬取
    async runWebScraper(type = 'all') {
        if (!this.isApiAvailable) {
            console.warn('⚠️ API服务不可用，无法运行网络爬取');
            return false;
        }

        try {
            const response = await fetch(`${this.baseUrl}/api/scraper/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type: type })
            });

            const data = await response.json();
            if (data.success) {
                console.log(`🌐 网络爬取完成，获取 ${data.total} 条数据`);
                return data;
            } else {
                console.error('❌ 网络爬取失败:', data.message);
                return false;
            }
        } catch (error) {
            console.error('❌ 网络爬取失败:', error);
            return false;
        }
    }

    // 生成模拟车流量数据
    generateTrafficFlowData(hours = 24) {
        const data = [];
        const now = new Date();
        const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);

        for (let i = 0; i < hours; i++) {
            const time = new Date(startTime.getTime() + i * 60 * 60 * 1000);
            const hour = time.getHours();

            let flow;
            if ((hour >= 6 && hour <= 9) || (hour >= 17 && hour <= 20)) {
                // 高峰期
                flow = 1200 + Math.floor(Math.random() * 300) + 200;
            } else if (hour >= 22 || hour <= 5) {
                // 夜间
                flow = Math.max(100, 1200 - Math.floor(Math.random() * 300) - 300);
            } else {
                // 平峰期
                flow = 1200 + Math.floor(Math.random() * 400) - 200;
            }

            data.push({
                time: time.toTimeString().substring(0, 5),
                flow: Math.max(100, flow),
                timestamp: time.toISOString()
            });
        }

        return data;
    }

    // 生成模拟车速数据
    generateSpeedData() {
        return [
            { speed_range: '0-20', count: Math.floor(Math.random() * 70) + 30 },
            { speed_range: '20-40', count: Math.floor(Math.random() * 100) + 150 },
            { speed_range: '40-60', count: Math.floor(Math.random() * 70) + 280 },
            { speed_range: '60-80', count: Math.floor(Math.random() * 100) + 150 },
            { speed_range: '80+', count: Math.floor(Math.random() * 70) + 30 }
        ];
    }

    // 生成模拟车辆类型数据
    generateVehicleTypesData() {
        return [
            { type: '小型车', count: Math.floor(Math.random() * 200) + 700, color: '#00d4ff' },
            { type: '中型车', count: Math.floor(Math.random() * 150) + 200, color: '#00ff88' },
            { type: '大型车', count: Math.floor(Math.random() * 100) + 100, color: '#ffd700' },
            { type: '货车', count: Math.floor(Math.random() * 70) + 50, color: '#ff6b6b' },
            { type: '客车', count: Math.floor(Math.random() * 40) + 20, color: '#9c88ff' }
        ];
    }

    // 生成模拟违法行为数据
    generateViolationData() {
        return [
            { type: '违法停车', count: Math.floor(Math.random() * 30) + 30 },
            { type: '超速行驶', count: Math.floor(Math.random() * 20) + 10 },
            { type: '闯红灯', count: Math.floor(Math.random() * 10) + 5 },
            { type: '逆向行驶', count: Math.floor(Math.random() * 7) + 1 },
            { type: '不按道行驶', count: Math.floor(Math.random() * 12) + 8 }
        ];
    }

    // 生成模拟实时数据
    generateRealtimeData() {
        return {
            current_traffic: Math.floor(Math.random() * 500) + 1000,
            average_speed: Math.floor(Math.random() * 20) + 65,
            road_occupancy: Math.round((Math.random() * 20 + 15) * 10) / 10,
            incident_count: Math.floor(Math.random() * 6),
            timestamp: new Date().toISOString()
        };
    }

    // 生成模拟事件数据
    generateEvents(count = 10) {
        const eventTypes = [
            { type: 'normal', desc: '车辆正常通过' },
            { type: 'normal', desc: '信号灯切换正常' },
            { type: 'warning', desc: '检测到超速车辆' },
            { type: 'warning', desc: '车流量异常增加' },
            { type: 'urgent', desc: '发现交通事故' },
            { type: 'urgent', desc: '违法停车' }
        ];

        const locations = ['东西向主道', '南北向辅道', '路口东南角', '主路口', '全路段'];
        const events = [];

        for (let i = 0; i < count; i++) {
            const event = eventTypes[Math.floor(Math.random() * eventTypes.length)];
            const location = locations[Math.floor(Math.random() * locations.length)];
            const timeOffset = Math.floor(Math.random() * 3600); // 最近1小时内
            const eventTime = new Date(Date.now() - timeOffset * 1000);

            events.push({
                id: i + 1,
                type: event.type,
                description: event.desc,
                location: location,
                time: eventTime.toTimeString().substring(0, 8),
                timestamp: eventTime.toISOString()
            });
        }

        return events.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }

    // 获取数据的统一接口
    async getData(endpoint, params = {}) {
        if (this.isApiAvailable) {
            try {
                const url = new URL(endpoint, this.baseUrl);
                Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                const response = await fetch(url);
                if (response.ok) {
                    const data = await response.json();
                    return data;
                }
            } catch (error) {
                console.warn('API请求失败，使用模拟数据:', error);
            }
        }

        // 使用模拟数据
        return this.getMockData(endpoint, params);
    }

    // 获取模拟数据
    getMockData(endpoint, params = {}) {
        const mockData = {
            success: true,
            timestamp: new Date().toISOString()
        };

        switch (endpoint) {
            case '/api/traffic/flow':
                mockData.data = this.generateTrafficFlowData(params.hours || 24);
                mockData.total = mockData.data.length;
                break;

            case '/api/traffic/speed':
                mockData.data = this.generateSpeedData();
                break;

            case '/api/traffic/vehicles':
                mockData.data = this.generateVehicleTypesData();
                break;

            case '/api/traffic/violations':
                mockData.data = this.generateViolationData();
                break;

            case '/api/traffic/realtime':
                mockData.data = this.generateRealtimeData();
                break;

            case '/api/traffic/events':
                mockData.data = this.generateEvents(params.count || 10);
                mockData.total = mockData.data.length;
                break;

            default:
                mockData.success = false;
                mockData.error = 'Unknown endpoint';
                break;
        }

        return Promise.resolve(mockData);
    }

    // 获取路口数据
    async getIntersectionData(intersectionId) {
        const mockData = {
            success: true,
            data: {
                intersection_id: intersectionId,
                name: `路口${intersectionId}`,
                traffic_light_status: {
                    current_phase: ['red', 'yellow', 'green'][Math.floor(Math.random() * 3)],
                    remaining_time: Math.floor(Math.random() * 25) + 5
                },
                vehicle_count: {
                    east_west: Math.floor(Math.random() * 10) + 5,
                    north_south: Math.floor(Math.random() * 9) + 3
                },
                average_speed: Math.floor(Math.random() * 20) + 25,
                incidents: Math.floor(Math.random() * 4)
            },
            timestamp: new Date().toISOString()
        };

        return Promise.resolve(mockData);
    }
}

// 创建全局API服务实例
window.mockApiService = new MockApiService();

// 导出服务
window.MockApiService = MockApiService;