// 路口3D场景管理类
class IntersectionScene {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.vehicles = [];
        this.trafficLights = [];
        this.animationId = null;
        this.init();
    }

    // 初始化3D场景
    init() {
        const container = document.getElementById('intersectionView');
        if (!container) return;

        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a237e);

        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            60,
            container.clientWidth / container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 25, 25);
        this.camera.lookAt(0, 0, 0);

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true, 
            alpha: true 
        });
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.setClearColor(0x000000, 0);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        container.appendChild(this.renderer.domElement);

        // 添加光源
        this.addLights();

        // 创建路口
        this.createIntersection();

        // 创建车辆
        this.createVehicles();

        // 创建信号灯
        this.createTrafficLights();

        // 开始渲染循环
        this.animate();

        // 监听窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
    }

    // 添加光源
    addLights() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // 方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(20, 20, 10);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // 路口照明
        const spotLight1 = new THREE.SpotLight(0x64b5f6, 1, 50, Math.PI / 6);
        spotLight1.position.set(-10, 15, -10);
        spotLight1.target.position.set(0, 0, 0);
        this.scene.add(spotLight1);
        this.scene.add(spotLight1.target);

        const spotLight2 = new THREE.SpotLight(0x64b5f6, 1, 50, Math.PI / 6);
        spotLight2.position.set(10, 15, 10);
        spotLight2.target.position.set(0, 0, 0);
        this.scene.add(spotLight2);
        this.scene.add(spotLight2.target);
    }

    // 创建路口
    createIntersection() {
        // 主路面
        const roadGeometry = new THREE.PlaneGeometry(40, 40);
        const roadMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x2c2c2c,
            transparent: true,
            opacity: 0.9
        });
        const road = new THREE.Mesh(roadGeometry, roadMaterial);
        road.rotation.x = -Math.PI / 2;
        road.receiveShadow = true;
        this.scene.add(road);

        // 创建十字路口标线
        this.createRoadMarkings();

        // 创建人行道
        this.createSidewalks();

        // 创建建筑物
        this.createBuildings();
    }

    // 创建道路标线
    createRoadMarkings() {
        const lineGeometry = new THREE.BoxGeometry(0.3, 0.02, 3);
        const lineMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

        // 东西向车道分隔线
        for (let i = -15; i <= 15; i += 6) {
            if (Math.abs(i) > 3) { // 避开路口中心
                const line = new THREE.Mesh(lineGeometry, lineMaterial);
                line.position.set(i, 0.01, 0);
                line.rotation.y = Math.PI / 2;
                this.scene.add(line);
            }
        }

        // 南北向车道分隔线
        for (let i = -15; i <= 15; i += 6) {
            if (Math.abs(i) > 3) { // 避开路口中心
                const line = new THREE.Mesh(lineGeometry, lineMaterial);
                line.position.set(0, 0.01, i);
                this.scene.add(line);
            }
        }

        // 停止线
        const stopLineGeometry = new THREE.BoxGeometry(8, 0.02, 0.5);
        const stopLineMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

        // 四个方向的停止线
        const stopLine1 = new THREE.Mesh(stopLineGeometry, stopLineMaterial);
        stopLine1.position.set(0, 0.01, -8);
        this.scene.add(stopLine1);

        const stopLine2 = new THREE.Mesh(stopLineGeometry, stopLineMaterial);
        stopLine2.position.set(0, 0.01, 8);
        this.scene.add(stopLine2);

        const stopLine3 = new THREE.Mesh(stopLineGeometry, stopLineMaterial);
        stopLine3.position.set(-8, 0.01, 0);
        stopLine3.rotation.y = Math.PI / 2;
        this.scene.add(stopLine3);

        const stopLine4 = new THREE.Mesh(stopLineGeometry, stopLineMaterial);
        stopLine4.position.set(8, 0.01, 0);
        stopLine4.rotation.y = Math.PI / 2;
        this.scene.add(stopLine4);
    }

    // 创建人行道
    createSidewalks() {
        const sidewalkGeometry = new THREE.BoxGeometry(2, 0.2, 2);
        const sidewalkMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });

        // 四个角的人行道
        const positions = [
            [-15, 0.1, -15], [15, 0.1, -15],
            [-15, 0.1, 15], [15, 0.1, 15]
        ];

        positions.forEach(pos => {
            const sidewalk = new THREE.Mesh(sidewalkGeometry, sidewalkMaterial);
            sidewalk.position.set(pos[0], pos[1], pos[2]);
            sidewalk.castShadow = true;
            this.scene.add(sidewalk);
        });
    }

    // 创建建筑物
    createBuildings() {
        const buildingGeometry = new THREE.BoxGeometry(3, 8, 3);
        const buildingMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5568 });

        // 周围建筑物
        const buildingPositions = [
            [-18, 4, -18], [18, 4, -18],
            [-18, 4, 18], [18, 4, 18]
        ];

        buildingPositions.forEach(pos => {
            const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
            building.position.set(pos[0], pos[1], pos[2]);
            building.castShadow = true;
            this.scene.add(building);

            // 添加窗户
            this.addWindows(building);
        });
    }

    // 添加窗户
    addWindows(building) {
        const windowGeometry = new THREE.PlaneGeometry(0.5, 0.5);
        const windowMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xffeb3b,
            emissive: 0xffeb3b,
            emissiveIntensity: 0.3
        });

        for (let y = 1; y < 7; y += 2) {
            for (let x = -1; x <= 1; x += 2) {
                const window = new THREE.Mesh(windowGeometry, windowMaterial);
                window.position.set(x, y, 1.51);
                building.add(window);
            }
        }
    }

    // 创建车辆
    createVehicles() {
        const vehicleGeometry = new THREE.BoxGeometry(1.8, 1, 4);
        const vehicleColors = [0x64b5f6, 0x81c784, 0xffb74d, 0xe57373, 0xba68c8];

        for (let i = 0; i < 12; i++) {
            const color = vehicleColors[Math.floor(Math.random() * vehicleColors.length)];
            const vehicleMaterial = new THREE.MeshLambertMaterial({ color });
            const vehicle = new THREE.Mesh(vehicleGeometry, vehicleMaterial);

            // 随机选择车道
            const lanes = [
                { x: -3, z: 0, direction: 'east' },
                { x: 3, z: 0, direction: 'west' },
                { x: 0, z: -3, direction: 'north' },
                { x: 0, z: 3, direction: 'south' }
            ];

            const lane = lanes[Math.floor(Math.random() * lanes.length)];
            
            if (lane.direction === 'east' || lane.direction === 'west') {
                vehicle.position.set(
                    (Math.random() - 0.5) * 30,
                    0.5,
                    lane.z + (Math.random() - 0.5) * 2
                );
                if (lane.direction === 'west') vehicle.rotation.y = Math.PI;
            } else {
                vehicle.position.set(
                    lane.x + (Math.random() - 0.5) * 2,
                    0.5,
                    (Math.random() - 0.5) * 30
                );
                if (lane.direction === 'north') vehicle.rotation.y = -Math.PI / 2;
                else vehicle.rotation.y = Math.PI / 2;
            }

            vehicle.userData = {
                speed: Math.random() * 0.1 + 0.05,
                direction: lane.direction,
                lane: lane
            };

            vehicle.castShadow = true;
            this.vehicles.push(vehicle);
            this.scene.add(vehicle);
        }
    }

    // 创建信号灯
    createTrafficLights() {
        const poleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 5);
        const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

        const lightPositions = [
            { x: -10, z: -10 }, { x: 10, z: -10 },
            { x: -10, z: 10 }, { x: 10, z: 10 }
        ];

        lightPositions.forEach(pos => {
            // 信号灯杆
            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
            pole.position.set(pos.x, 2.5, pos.z);
            pole.castShadow = true;
            this.scene.add(pole);

            // 信号灯箱
            const lightBoxGeometry = new THREE.BoxGeometry(0.5, 1.5, 0.3);
            const lightBoxMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
            const lightBox = new THREE.Mesh(lightBoxGeometry, lightBoxMaterial);
            lightBox.position.set(pos.x, 4.5, pos.z);
            this.scene.add(lightBox);

            // 三色灯
            this.createLights(lightBox, pos);
        });
    }

    // 创建三色灯
    createLights(lightBox, position) {
        const lightGeometry = new THREE.SphereGeometry(0.15);
        const colors = [0xff0000, 0xffff00, 0x00ff00]; // 红、黄、绿
        const lights = [];

        colors.forEach((color, index) => {
            const lightMaterial = new THREE.MeshBasicMaterial({ 
                color: color,
                transparent: true,
                opacity: 0.3
            });
            const light = new THREE.Mesh(lightGeometry, lightMaterial);
            light.position.set(0, 0.4 - index * 0.4, 0.16);
            lightBox.add(light);
            lights.push(light);
        });

        this.trafficLights.push({
            lights: lights,
            currentLight: 0, // 0: 红, 1: 黄, 2: 绿
            timer: 0,
            position: position
        });
    }

    // 更新车辆位置
    updateVehicles() {
        this.vehicles.forEach(vehicle => {
            const userData = vehicle.userData;
            
            switch (userData.direction) {
                case 'east':
                    vehicle.position.x += userData.speed;
                    if (vehicle.position.x > 20) {
                        vehicle.position.x = -20;
                    }
                    break;
                case 'west':
                    vehicle.position.x -= userData.speed;
                    if (vehicle.position.x < -20) {
                        vehicle.position.x = 20;
                    }
                    break;
                case 'north':
                    vehicle.position.z -= userData.speed;
                    if (vehicle.position.z < -20) {
                        vehicle.position.z = 20;
                    }
                    break;
                case 'south':
                    vehicle.position.z += userData.speed;
                    if (vehicle.position.z > 20) {
                        vehicle.position.z = -20;
                    }
                    break;
            }
        });
    }

    // 更新信号灯
    updateTrafficLights() {
        this.trafficLights.forEach(trafficLight => {
            trafficLight.timer++;
            
            // 每120帧切换一次信号灯（约2秒）
            if (trafficLight.timer >= 120) {
                trafficLight.timer = 0;
                
                // 关闭当前灯
                trafficLight.lights[trafficLight.currentLight].material.opacity = 0.3;
                
                // 切换到下一个灯
                trafficLight.currentLight = (trafficLight.currentLight + 1) % 3;
                
                // 开启新灯
                trafficLight.lights[trafficLight.currentLight].material.opacity = 1;
            }
        });
    }

    // 动画循环
    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());

        // 更新车辆
        this.updateVehicles();

        // 更新信号灯
        this.updateTrafficLights();

        // 相机轻微旋转
        const time = Date.now() * 0.0002;
        this.camera.position.x = Math.sin(time) * 3;
        this.camera.position.z = Math.cos(time) * 3 + 25;
        this.camera.lookAt(0, 0, 0);

        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }

    // 处理窗口大小变化
    handleResize() {
        const container = document.getElementById('intersectionView');
        if (!container) return;

        this.camera.aspect = container.clientWidth / container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(container.clientWidth, container.clientHeight);
    }

    // 销毁场景
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
    }
}

// 导出场景类
window.IntersectionScene = IntersectionScene;
