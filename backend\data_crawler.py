# -*- coding: utf-8 -*-
"""
数据爬取核心模块
"""

import requests
import json
import time
import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue

from crawler_config import CrawlerConfig, LOCATION_CONFIG, DATA_TRANSFORM_RULES
from models import db, TrafficFlow, VehicleSpeed, TrafficEvent, DeviceStatus, ViolationRecord, IntersectionStatus

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataCrawler:
    """数据爬取器"""
    
    def __init__(self, app=None):
        self.app = app
        self.session = requests.Session()
        self.session.headers.update(CrawlerConfig.DEFAULT_HEADERS)
        self.data_queue = Queue()
        self.is_running = False
        self.threads = []
        
    def init_app(self, app):
        """初始化Flask应用"""
        self.app = app
        
    def start_crawling(self):
        """启动爬取任务"""
        if self.is_running:
            logger.warning("爬取任务已在运行中")
            return
            
        self.is_running = True
        logger.info("🚀 启动数据爬取服务")
        
        # 启动各种爬取任务
        for task_name, task_config in CrawlerConfig.CRAWL_TASKS.items():
            if task_config['enabled']:
                thread = threading.Thread(
                    target=self._run_crawl_task,
                    args=(task_name, task_config),
                    daemon=True
                )
                thread.start()
                self.threads.append(thread)
                logger.info(f"✅ 启动爬取任务: {task_config['name']}")
        
        # 启动数据处理线程
        processor_thread = threading.Thread(target=self._process_data_queue, daemon=True)
        processor_thread.start()
        self.threads.append(processor_thread)
        
    def stop_crawling(self):
        """停止爬取任务"""
        self.is_running = False
        logger.info("🛑 停止数据爬取服务")
        
    def _run_crawl_task(self, task_name: str, task_config: Dict):
        """运行单个爬取任务"""
        while self.is_running:
            try:
                logger.info(f"📡 执行爬取任务: {task_config['name']}")
                
                # 根据任务类型执行不同的爬取逻辑
                if task_name == 'traffic_flow':
                    self._crawl_traffic_flow()
                elif task_name == 'traffic_events':
                    self._crawl_traffic_events()
                elif task_name == 'device_status':
                    self._crawl_device_status()
                elif task_name == 'violation_records':
                    self._crawl_violation_records()
                    
                # 等待下次执行
                time.sleep(task_config['interval'])
                
            except Exception as e:
                logger.error(f"❌ 爬取任务 {task_name} 执行失败: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试
                
    def _crawl_traffic_flow(self):
        """爬取车流量数据"""
        try:
            # 模拟从多个数据源获取数据
            data_sources = [
                self._fetch_mock_traffic_data(),
                self._fetch_simulated_sensor_data(),
                self._fetch_third_party_api_data()
            ]
            
            for data_list in data_sources:
                if data_list:
                    for data in data_list:
                        self.data_queue.put(('traffic_flow', data))
                        
        except Exception as e:
            logger.error(f"❌ 爬取车流量数据失败: {e}")
            
    def _crawl_traffic_events(self):
        """爬取交通事件数据"""
        try:
            # 模拟从交通管理部门API获取事件数据
            events_data = self._fetch_traffic_events_from_api()
            
            for event in events_data:
                self.data_queue.put(('traffic_event', event))
                
        except Exception as e:
            logger.error(f"❌ 爬取交通事件数据失败: {e}")
            
    def _crawl_device_status(self):
        """爬取设备状态数据"""
        try:
            # 模拟从设备监控系统获取状态数据
            device_data = self._fetch_device_status_from_system()
            
            for device in device_data:
                self.data_queue.put(('device_status', device))
                
        except Exception as e:
            logger.error(f"❌ 爬取设备状态数据失败: {e}")
            
    def _crawl_violation_records(self):
        """爬取违法记录数据"""
        try:
            # 模拟从违法处理系统获取数据
            violation_data = self._fetch_violation_records_from_system()
            
            for violation in violation_data:
                self.data_queue.put(('violation_record', violation))
                
        except Exception as e:
            logger.error(f"❌ 爬取违法记录数据失败: {e}")
            
    def _fetch_mock_traffic_data(self) -> List[Dict]:
        """获取模拟交通数据"""
        try:
            # 使用公开API模拟数据获取
            response = self.session.get(
                'https://jsonplaceholder.typicode.com/posts',
                timeout=CrawlerConfig.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                # 将获取的数据转换为交通流量数据格式
                posts = response.json()[:10]  # 只取前10条
                traffic_data = []
                
                for i, post in enumerate(posts):
                    location = LOCATION_CONFIG['default_locations'][i % len(LOCATION_CONFIG['default_locations'])]
                    
                    # 根据帖子ID生成模拟的车流量数据
                    flow_count = (post['id'] * 7 + len(post['title'])) % 200 + 50
                    
                    traffic_data.append({
                        'location_id': location['id'],
                        'location_name': location['name'],
                        'flow_count': flow_count,
                        'record_time': datetime.utcnow(),
                        'vehicle_type': random.choice(['小型车', '中型车', '大型车', '货车']),
                        'direction': random.choice(['东向西', '西向东', '南向北', '北向南']),
                        'source': 'mock_api'
                    })
                
                logger.info(f"✅ 获取模拟交通数据 {len(traffic_data)} 条")
                return traffic_data
                
        except Exception as e:
            logger.error(f"❌ 获取模拟交通数据失败: {e}")
            
        return []
        
    def _fetch_simulated_sensor_data(self) -> List[Dict]:
        """获取模拟传感器数据"""
        sensor_data = []
        
        try:
            # 模拟从多个传感器获取数据
            for location in LOCATION_CONFIG['default_locations']:
                # 生成随机的传感器数据
                base_flow = random.randint(80, 150)
                
                # 根据时间调整流量
                current_hour = datetime.utcnow().hour
                if 7 <= current_hour <= 9 or 17 <= current_hour <= 19:
                    base_flow = int(base_flow * 1.5)  # 高峰期增加50%
                elif 22 <= current_hour or current_hour <= 6:
                    base_flow = int(base_flow * 0.3)  # 夜间减少70%
                
                sensor_data.append({
                    'location_id': location['id'],
                    'location_name': location['name'],
                    'flow_count': base_flow + random.randint(-20, 20),
                    'record_time': datetime.utcnow(),
                    'vehicle_type': 'all',
                    'direction': 'all',
                    'source': 'sensor'
                })
                
            logger.info(f"✅ 生成传感器数据 {len(sensor_data)} 条")
            
        except Exception as e:
            logger.error(f"❌ 生成传感器数据失败: {e}")
            
        return sensor_data
        
    def _fetch_third_party_api_data(self) -> List[Dict]:
        """获取第三方API数据"""
        try:
            # 模拟调用第三方交通数据API
            # 这里可以替换为真实的API调用
            
            api_data = []
            
            # 模拟API返回的数据格式
            for location in LOCATION_CONFIG['default_locations']:
                # 生成符合真实API格式的数据
                flow_data = {
                    'location_id': location['id'],
                    'location_name': location['name'],
                    'flow_count': random.randint(60, 180),
                    'record_time': datetime.utcnow(),
                    'vehicle_type': random.choice(['小型车', '中型车', '大型车']),
                    'direction': random.choice(['东向西', '西向东', '南向北', '北向南']),
                    'source': 'third_party_api',
                    'confidence': random.uniform(0.8, 1.0)  # 数据置信度
                }
                api_data.append(flow_data)
                
            logger.info(f"✅ 获取第三方API数据 {len(api_data)} 条")
            return api_data
            
        except Exception as e:
            logger.error(f"❌ 获取第三方API数据失败: {e}")
            
        return []
        
    def _fetch_traffic_events_from_api(self) -> List[Dict]:
        """从API获取交通事件数据"""
        events = []
        
        try:
            # 模拟交通事件数据
            event_types = ['accident', 'congestion', 'construction', 'weather']
            event_levels = ['normal', 'warning', 'urgent']
            
            # 随机生成1-3个事件
            for _ in range(random.randint(1, 3)):
                location = random.choice(LOCATION_CONFIG['default_locations'])
                event_type = random.choice(event_types)
                event_level = random.choice(event_levels)
                
                event = {
                    'event_type': event_type,
                    'event_level': event_level,
                    'location_id': location['id'],
                    'location_name': location['name'],
                    'description': f'{location["name"]}发生{event_type}',
                    'start_time': datetime.utcnow() - timedelta(minutes=random.randint(1, 60)),
                    'status': 'active',
                    'source': 'traffic_management_api'
                }
                events.append(event)
                
            logger.info(f"✅ 获取交通事件数据 {len(events)} 条")
            
        except Exception as e:
            logger.error(f"❌ 获取交通事件数据失败: {e}")
            
        return events
        
    def _fetch_device_status_from_system(self) -> List[Dict]:
        """从系统获取设备状态数据"""
        devices = []
        
        try:
            # 模拟设备状态数据
            device_types = ['camera', 'traffic_light', 'detector', 'sensor']
            
            for i, location in enumerate(LOCATION_CONFIG['default_locations']):
                for device_type in device_types:
                    device_id = f"{device_type.upper()}{location['id'][-3:]}"
                    
                    # 90%概率设备在线
                    status = 'online' if random.random() > 0.1 else 'offline'
                    
                    device = {
                        'device_id': device_id,
                        'device_name': f'{device_type}_{location["name"][:10]}',
                        'device_type': device_type,
                        'location_id': location['id'],
                        'status': status,
                        'last_heartbeat': datetime.utcnow() - timedelta(minutes=random.randint(1, 10)),
                        'source': 'device_monitoring_system'
                    }
                    devices.append(device)
                    
            logger.info(f"✅ 获取设备状态数据 {len(devices)} 条")
            
        except Exception as e:
            logger.error(f"❌ 获取设备状态数据失败: {e}")
            
        return devices
        
    def _fetch_violation_records_from_system(self) -> List[Dict]:
        """从系统获取违法记录数据"""
        violations = []
        
        try:
            # 模拟违法记录数据
            violation_types = ['超速行驶', '违法停车', '闯红灯', '逆向行驶', '不按道行驶']
            
            # 随机生成0-5条违法记录
            for _ in range(random.randint(0, 5)):
                location = random.choice(LOCATION_CONFIG['default_locations'])
                violation_type = random.choice(violation_types)
                
                violation = {
                    'vehicle_id': f'车牌{random.choice(["苏A", "苏B", "苏C"])}{random.randint(10000, 99999)}',
                    'violation_type': violation_type,
                    'location_id': location['id'],
                    'location_name': location['name'],
                    'description': f'在{location["name"]}发生{violation_type}',
                    'record_time': datetime.utcnow() - timedelta(minutes=random.randint(1, 30)),
                    'status': 'pending',
                    'source': 'violation_detection_system'
                }
                violations.append(violation)
                
            logger.info(f"✅ 获取违法记录数据 {len(violations)} 条")
            
        except Exception as e:
            logger.error(f"❌ 获取违法记录数据失败: {e}")
            
        return violations
        
    def _process_data_queue(self):
        """处理数据队列"""
        while self.is_running:
            try:
                if not self.data_queue.empty():
                    data_type, data = self.data_queue.get(timeout=1)
                    self._save_data_to_database(data_type, data)
                else:
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"❌ 处理数据队列失败: {e}")
                time.sleep(5)
                
    def _save_data_to_database(self, data_type: str, data: Dict):
        """保存数据到数据库"""
        try:
            with self.app.app_context():
                if data_type == 'traffic_flow':
                    traffic_flow = TrafficFlow(
                        location_id=data['location_id'],
                        location_name=data['location_name'],
                        flow_count=data['flow_count'],
                        record_time=data['record_time'],
                        vehicle_type=data.get('vehicle_type', 'all'),
                        direction=data.get('direction', 'unknown')
                    )
                    db.session.add(traffic_flow)
                    
                elif data_type == 'traffic_event':
                    traffic_event = TrafficEvent(
                        event_type=data['event_type'],
                        event_level=data['event_level'],
                        location_id=data['location_id'],
                        location_name=data['location_name'],
                        description=data['description'],
                        start_time=data['start_time'],
                        status=data['status']
                    )
                    db.session.add(traffic_event)
                    
                elif data_type == 'device_status':
                    # 更新或创建设备状态
                    device = DeviceStatus.query.filter_by(device_id=data['device_id']).first()
                    if device:
                        device.status = data['status']
                        device.last_heartbeat = data['last_heartbeat']
                        device.updated_at = datetime.utcnow()
                    else:
                        device = DeviceStatus(
                            device_id=data['device_id'],
                            device_name=data['device_name'],
                            device_type=data['device_type'],
                            location_id=data['location_id'],
                            status=data['status'],
                            last_heartbeat=data['last_heartbeat']
                        )
                        db.session.add(device)
                        
                elif data_type == 'violation_record':
                    violation = ViolationRecord(
                        vehicle_id=data['vehicle_id'],
                        violation_type=data['violation_type'],
                        location_id=data['location_id'],
                        location_name=data['location_name'],
                        description=data['description'],
                        record_time=data['record_time'],
                        status=data['status']
                    )
                    db.session.add(violation)
                    
                db.session.commit()
                logger.debug(f"✅ 保存 {data_type} 数据到数据库")
                
        except Exception as e:
            db.session.rollback()
            logger.error(f"❌ 保存 {data_type} 数据到数据库失败: {e}")

# 创建全局爬虫实例
crawler = DataCrawler()
