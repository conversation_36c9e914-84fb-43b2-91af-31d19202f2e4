# 智慧城市交通流量可视化数据分析系统 - 完整部署指南

## 🎯 系统概述

本系统是一个完整的智慧城市交通流量可视化数据分析平台，包含：

- **前端可视化界面** - 两个专业的交通监控大屏
- **后端API服务** - RESTful API接口服务
- **MySQL数据库** - 存储交通数据
- **数据爬虫系统** - 自动从网络获取交通数据
- **实时数据处理** - 数据清洗、转换和分析

## 📋 系统要求

### 软件环境
- **操作系统**: Windows 10/11, Linux, macOS
- **Python**: 3.7+ (推荐 3.9+)
- **MySQL**: 5.7+ 或 8.0+
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+

### 硬件要求
- **内存**: 4GB+ (推荐 8GB+)
- **存储**: 2GB+ 可用空间
- **网络**: 稳定的互联网连接

## 🚀 快速部署

### 方法一：一键启动（推荐）

1. **下载项目文件**
   ```bash
   # 确保所有项目文件在同一目录下
   智慧城市交通流量/
   ├── frontend/
   ├── backend/
   ├── start.bat
   └── README.md
   ```

2. **安装MySQL**
   - 下载并安装MySQL 8.0
   - 设置root用户密码为 `root`
   - 启动MySQL服务

3. **运行启动脚本**
   ```bash
   # Windows
   双击 start.bat
   
   # Linux/macOS
   chmod +x start.sh
   ./start.sh
   ```

4. **访问系统**
   - 导航页面: http://localhost:8080/navigation.html
   - 主界面: http://localhost:8080/index.html
   - 路口监控: http://localhost:8080/intersection.html

### 方法二：手动部署

#### 步骤1：安装Python依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 步骤2：配置数据库
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE smart_traffic CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 退出MySQL
exit
```

#### 步骤3：初始化数据库
```bash
cd backend
python init_db.py
```

#### 步骤4：启动后端服务
```bash
cd backend
python run_server.py
```

#### 步骤5：启动前端服务
```bash
cd frontend
python -m http.server 8080
```

## 🔧 配置说明

### 数据库配置

编辑 `backend/config.py` 文件：

```python
class Config:
    MYSQL_HOST = 'localhost'        # MySQL主机地址
    MYSQL_PORT = 3306              # MySQL端口
    MYSQL_USER = 'root'            # MySQL用户名
    MYSQL_PASSWORD = 'root'        # MySQL密码
    MYSQL_DATABASE = 'smart_traffic'  # 数据库名
```

### 爬虫配置

编辑 `backend/crawler_config.py` 文件：

```python
CRAWL_TASKS = {
    'traffic_flow': {
        'interval': 300,  # 爬取间隔（秒）
        'enabled': True   # 是否启用
    }
}
```

## 📊 数据库结构

系统自动创建以下数据表：

### 1. traffic_flow - 车流量数据表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| location_id | VARCHAR(50) | 位置ID |
| location_name | VARCHAR(100) | 位置名称 |
| flow_count | INT | 车流量 |
| record_time | DATETIME | 记录时间 |
| vehicle_type | VARCHAR(20) | 车辆类型 |
| direction | VARCHAR(20) | 行驶方向 |

### 2. vehicle_speed - 车辆速度表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| vehicle_id | VARCHAR(50) | 车辆ID |
| location_id | VARCHAR(50) | 位置ID |
| speed | FLOAT | 车速(km/h) |
| speed_limit | FLOAT | 限速(km/h) |
| record_time | DATETIME | 记录时间 |
| is_violation | BOOLEAN | 是否超速 |

### 3. traffic_event - 交通事件表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| event_type | VARCHAR(20) | 事件类型 |
| event_level | VARCHAR(20) | 事件级别 |
| location_id | VARCHAR(50) | 位置ID |
| location_name | VARCHAR(100) | 位置名称 |
| description | TEXT | 事件描述 |
| status | VARCHAR(20) | 事件状态 |
| start_time | DATETIME | 开始时间 |
| end_time | DATETIME | 结束时间 |

### 4. device_status - 设备状态表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| device_id | VARCHAR(50) | 设备ID |
| device_name | VARCHAR(100) | 设备名称 |
| device_type | VARCHAR(20) | 设备类型 |
| location_id | VARCHAR(50) | 位置ID |
| status | VARCHAR(20) | 设备状态 |
| last_heartbeat | DATETIME | 最后心跳时间 |

### 5. violation_record - 违法记录表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| vehicle_id | VARCHAR(50) | 车辆ID |
| violation_type | VARCHAR(20) | 违法类型 |
| location_id | VARCHAR(50) | 位置ID |
| location_name | VARCHAR(100) | 位置名称 |
| description | TEXT | 违法描述 |
| record_time | DATETIME | 违法时间 |
| status | VARCHAR(20) | 处理状态 |

### 6. intersection_status - 路口状态表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| intersection_id | VARCHAR(50) | 路口ID |
| intersection_name | VARCHAR(100) | 路口名称 |
| traffic_light_phase | VARCHAR(10) | 信号灯相位 |
| phase_remaining_time | INT | 相位剩余时间 |
| east_west_flow | INT | 东西向车流量 |
| north_south_flow | INT | 南北向车流量 |
| average_speed | FLOAT | 平均车速 |
| incident_count | INT | 事件数量 |

## 🕷️ 数据爬取功能

### 爬取数据源

1. **模拟API数据源**
   - JSONPlaceholder API (用于演示)
   - 转换为交通数据格式

2. **政府网站数据**
   - 交通运输部网站
   - 各地交通委员会网站

3. **新闻网站数据**
   - 交通相关新闻事件
   - 自动提取交通信息

4. **社交媒体数据**
   - 交通相关社交媒体内容
   - 实时交通状况报告

5. **天气影响数据**
   - 天气API数据
   - 天气对交通的影响分析

### 爬取任务配置

```python
CRAWL_TASKS = {
    'traffic_flow': {
        'name': '车流量数据爬取',
        'interval': 300,  # 5分钟
        'enabled': True
    },
    'traffic_events': {
        'name': '交通事件爬取', 
        'interval': 600,  # 10分钟
        'enabled': True
    },
    'device_status': {
        'name': '设备状态爬取',
        'interval': 180,  # 3分钟
        'enabled': True
    }
}
```

## 🔗 API接口文档

### 基础接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/` | GET | API首页和文档 |
| `/api/traffic/flow` | GET | 获取车流量数据 |
| `/api/traffic/speed` | GET | 获取车速分布数据 |
| `/api/traffic/vehicles` | GET | 获取车辆类型统计 |
| `/api/traffic/violations` | GET | 获取违法行为统计 |
| `/api/traffic/realtime` | GET | 获取实时数据 |
| `/api/traffic/events` | GET | 获取事件列表 |
| `/api/traffic/intersection/<id>` | GET | 获取路口数据 |

### 爬虫控制接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/crawler/start` | POST | 启动数据爬虫 |
| `/api/crawler/stop` | POST | 停止数据爬虫 |
| `/api/crawler/status` | GET | 获取爬虫状态 |
| `/api/scraper/run` | POST | 运行网络爬取 |

### 数据库管理接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/database/init` | POST | 初始化数据库 |
| `/api/database/stats` | GET | 获取数据库统计 |

## 🎮 使用指南

### 1. 系统启动

1. 运行 `start.bat` 启动所有服务
2. 等待服务启动完成（约30秒）
3. 打开浏览器访问导航页面

### 2. 界面操作

**主界面功能：**
- 查看实时车流量趋势
- 监控3D道路场景
- 查看车辆轨迹和事件
- 控制监测预警系统

**路口监控功能：**
- 查看路口3D场景
- 控制信号灯系统
- 监控违法行为
- 处理交通事件

### 3. 数据管理

**查看数据库统计：**
```bash
curl http://localhost:5000/api/database/stats
```

**启动数据爬虫：**
```bash
curl -X POST http://localhost:5000/api/crawler/start
```

**运行网络爬取：**
```bash
curl -X POST http://localhost:5000/api/scraper/run \
  -H "Content-Type: application/json" \
  -d '{"type": "all"}'
```

## 🔍 故障排除

### 常见问题

#### 1. MySQL连接失败
**问题**: `pymysql.err.OperationalError: (2003, "Can't connect to MySQL server")`

**解决方案**:
- 检查MySQL服务是否启动
- 确认用户名密码正确
- 检查防火墙设置

#### 2. 端口被占用
**问题**: `OSError: [Errno 48] Address already in use`

**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :5000
netstat -ano | findstr :8080

# 终止进程
taskkill /PID <进程ID> /F
```

#### 3. Python依赖包安装失败
**问题**: `pip install` 失败

**解决方案**:
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 4. 数据库权限问题
**问题**: `Access denied for user 'root'@'localhost'`

**解决方案**:
```sql
-- 重置root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'root';
FLUSH PRIVILEGES;
```

### 日志查看

**后端日志**:
```bash
tail -f backend/logs/crawler.log
```

**数据库日志**:
```bash
# MySQL错误日志位置
# Windows: C:\ProgramData\MySQL\MySQL Server 8.0\Data\*.err
# Linux: /var/log/mysql/error.log
```

## 📈 性能优化

### 数据库优化

1. **索引优化**
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_traffic_flow_time ON traffic_flow(record_time);
CREATE INDEX idx_traffic_flow_location ON traffic_flow(location_id);
CREATE INDEX idx_vehicle_speed_time ON vehicle_speed(record_time);
```

2. **数据清理**
```sql
-- 清理30天前的数据
DELETE FROM traffic_flow WHERE record_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
DELETE FROM vehicle_speed WHERE record_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 爬虫优化

1. **调整爬取频率**
```python
# 根据数据重要性调整间隔
CRAWL_TASKS = {
    'traffic_flow': {'interval': 180},    # 重要数据，3分钟
    'traffic_events': {'interval': 300},  # 一般数据，5分钟
    'device_status': {'interval': 600}    # 状态数据，10分钟
}
```

2. **并发控制**
```python
# 限制并发请求数量
REQUEST_CONCURRENCY = 5
REQUEST_DELAY = 2  # 请求间隔
```

## 🔒 安全配置

### 1. 数据库安全
```sql
-- 创建专用数据库用户
CREATE USER 'traffic_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON smart_traffic.* TO 'traffic_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. API安全
```python
# 添加API密钥验证
API_KEY = 'your-secure-api-key'

# 限制请求频率
RATE_LIMIT = '100/hour'
```

### 3. 网络安全
- 使用HTTPS协议
- 配置防火墙规则
- 定期更新依赖包

## 📞 技术支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志文件
3. 确认环境配置正确
4. 验证网络连接正常

---

**版本**: v2.0.0  
**更新时间**: 2024年6月  
**支持**: 智慧城市交通流量可视化数据分析系统开发团队
