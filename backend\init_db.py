# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import random
from datetime import datetime, timedelta
from config import Config
from models import db, TrafficFlow, VehicleSpeed, TrafficEvent, DeviceStatus, ViolationRecord, IntersectionStatus

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {Config.MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 {Config.MYSQL_DATABASE} 创建成功")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def init_sample_data():
    """初始化示例数据"""
    try:
        # 清空现有数据
        db.session.query(TrafficFlow).delete()
        db.session.query(VehicleSpeed).delete()
        db.session.query(TrafficEvent).delete()
        db.session.query(DeviceStatus).delete()
        db.session.query(ViolationRecord).delete()
        db.session.query(IntersectionStatus).delete()
        
        # 1. 初始化设备状态数据
        devices = [
            {'device_id': 'CAM001', 'device_name': '摄像头01', 'device_type': 'camera', 'location_id': 'LOC001', 'status': 'online'},
            {'device_id': 'CAM002', 'device_name': '摄像头02', 'device_type': 'camera', 'location_id': 'LOC002', 'status': 'online'},
            {'device_id': 'TL001', 'device_name': '信号灯组01', 'device_type': 'traffic_light', 'location_id': 'LOC001', 'status': 'online'},
            {'device_id': 'TL002', 'device_name': '信号灯组02', 'device_type': 'traffic_light', 'location_id': 'LOC002', 'status': 'online'},
            {'device_id': 'DET001', 'device_name': '检测器01', 'device_type': 'detector', 'location_id': 'LOC001', 'status': 'offline'},
            {'device_id': 'DET002', 'device_name': '检测器02', 'device_type': 'detector', 'location_id': 'LOC002', 'status': 'online'},
        ]
        
        for device_data in devices:
            device = DeviceStatus(**device_data)
            device.last_heartbeat = datetime.utcnow() - timedelta(minutes=random.randint(1, 30))
            db.session.add(device)
        
        # 2. 初始化路口状态数据
        intersections = [
            {'intersection_id': 'INT001', 'intersection_name': '主要路口01', 'traffic_light_phase': 'red'},
            {'intersection_id': 'INT002', 'intersection_name': '主要路口02', 'traffic_light_phase': 'green'},
            {'intersection_id': 'INT003', 'intersection_name': '次要路口01', 'traffic_light_phase': 'yellow'},
        ]
        
        for intersection_data in intersections:
            intersection = IntersectionStatus(**intersection_data)
            intersection.phase_remaining_time = random.randint(10, 60)
            intersection.east_west_flow = random.randint(5, 20)
            intersection.north_south_flow = random.randint(3, 15)
            intersection.average_speed = random.randint(25, 50)
            intersection.incident_count = random.randint(0, 3)
            db.session.add(intersection)
        
        # 3. 初始化车流量历史数据（最近24小时）
        locations = [
            {'id': 'LOC001', 'name': 'G15沈海高速K1234+500'},
            {'id': 'LOC002', 'name': 'G15沈海高速K1235+200'},
            {'id': 'LOC003', 'name': '主要路口01'},
            {'id': 'LOC004', 'name': '主要路口02'},
        ]
        
        vehicle_types = ['小型车', '中型车', '大型车', '货车', '客车']
        directions = ['东向西', '西向东', '南向北', '北向南']
        
        # 生成最近24小时的数据
        for hour in range(24):
            record_time = datetime.utcnow() - timedelta(hours=23-hour)
            
            for location in locations:
                for vehicle_type in vehicle_types:
                    for direction in directions:
                        # 根据时间段调整车流量
                        if 6 <= record_time.hour <= 9 or 17 <= record_time.hour <= 20:
                            base_flow = 150  # 高峰期
                        elif 22 <= record_time.hour or record_time.hour <= 5:
                            base_flow = 30   # 夜间
                        else:
                            base_flow = 80   # 平峰期
                        
                        # 根据车型调整基数
                        if vehicle_type == '小型车':
                            flow_count = base_flow + random.randint(-20, 50)
                        elif vehicle_type in ['中型车', '大型车']:
                            flow_count = int(base_flow * 0.3) + random.randint(-10, 20)
                        else:
                            flow_count = int(base_flow * 0.1) + random.randint(-5, 10)
                        
                        flow_count = max(0, flow_count)
                        
                        traffic_flow = TrafficFlow(
                            location_id=location['id'],
                            location_name=location['name'],
                            flow_count=flow_count,
                            record_time=record_time,
                            vehicle_type=vehicle_type,
                            direction=direction
                        )
                        db.session.add(traffic_flow)
        
        # 4. 初始化车速数据（最近1小时）
        for minute in range(60):
            record_time = datetime.utcnow() - timedelta(minutes=59-minute)
            
            for i in range(random.randint(10, 30)):  # 每分钟10-30条记录
                location_id = random.choice([loc['id'] for loc in locations])
                speed = random.normalvariate(65, 15)  # 正态分布，均值65，标准差15
                speed = max(10, min(120, speed))  # 限制在10-120之间
                
                vehicle_speed = VehicleSpeed(
                    vehicle_id=f'V{random.randint(10000, 99999)}',
                    location_id=location_id,
                    speed=round(speed, 1),
                    speed_limit=60.0,
                    record_time=record_time,
                    is_violation=speed > 60
                )
                db.session.add(vehicle_speed)
        
        # 5. 初始化交通事件数据
        event_types = ['accident', 'congestion', 'construction', 'weather', 'violation']
        event_levels = ['normal', 'warning', 'urgent']
        
        for i in range(20):  # 创建20个事件
            start_time = datetime.utcnow() - timedelta(hours=random.randint(1, 48))
            location = random.choice(locations)
            event_type = random.choice(event_types)
            event_level = random.choice(event_levels)
            
            descriptions = {
                'accident': '交通事故',
                'congestion': '交通拥堵',
                'construction': '道路施工',
                'weather': '恶劣天气',
                'violation': '交通违法'
            }
            
            traffic_event = TrafficEvent(
                event_type=event_type,
                event_level=event_level,
                location_id=location['id'],
                location_name=location['name'],
                description=descriptions[event_type],
                status='active' if random.random() > 0.3 else 'resolved',
                start_time=start_time,
                end_time=start_time + timedelta(hours=random.randint(1, 6)) if random.random() > 0.5 else None
            )
            db.session.add(traffic_event)
        
        # 6. 初始化违法记录数据
        violation_types = ['超速行驶', '违法停车', '闯红灯', '逆向行驶', '不按道行驶']
        
        for i in range(50):  # 创建50条违法记录
            record_time = datetime.utcnow() - timedelta(hours=random.randint(1, 168))  # 最近一周
            location = random.choice(locations)
            violation_type = random.choice(violation_types)
            
            violation_record = ViolationRecord(
                vehicle_id=f'车牌{random.choice(["苏A", "苏B", "苏C"])}{random.randint(10000, 99999)}',
                violation_type=violation_type,
                location_id=location['id'],
                location_name=location['name'],
                description=f'在{location["name"]}发生{violation_type}',
                status=random.choice(['pending', 'processing', 'completed']),
                record_time=record_time
            )
            db.session.add(violation_record)
        
        # 提交所有数据
        db.session.commit()
        print("✅ 示例数据初始化成功")
        
        # 打印统计信息
        print(f"📊 数据统计:")
        print(f"   - 设备状态: {DeviceStatus.query.count()} 条")
        print(f"   - 路口状态: {IntersectionStatus.query.count()} 条")
        print(f"   - 车流量数据: {TrafficFlow.query.count()} 条")
        print(f"   - 车速数据: {VehicleSpeed.query.count()} 条")
        print(f"   - 交通事件: {TrafficEvent.query.count()} 条")
        print(f"   - 违法记录: {ViolationRecord.query.count()} 条")
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 初始化示例数据失败: {e}")
        raise e

if __name__ == '__main__':
    print("🚀 开始初始化数据库...")
    
    # 创建数据库
    if create_database():
        print("✅ 数据库创建完成")
    else:
        print("❌ 数据库创建失败，请检查MySQL连接配置")
        exit(1)
