#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧城市交通流量可视化数据分析系统 - 后端API
提供交通数据的RESTful API接口
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import random
import datetime
import json

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 模拟数据生成器
class TrafficDataGenerator:
    """交通数据生成器"""
    
    def __init__(self):
        self.base_traffic_flow = 1200  # 基础车流量
        self.base_speed = 75  # 基础车速
        
    def generate_traffic_flow_data(self, hours=24):
        """生成车流量数据"""
        data = []
        current_time = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        for i in range(hours):
            # 模拟一天中不同时段的车流量变化
            hour = current_time.hour
            if 6 <= hour <= 9 or 17 <= hour <= 20:  # 高峰期
                flow = self.base_traffic_flow + random.randint(200, 500)
            elif 22 <= hour or hour <= 5:  # 夜间
                flow = self.base_traffic_flow - random.randint(300, 600)
            else:  # 平峰期
                flow = self.base_traffic_flow + random.randint(-100, 200)
                
            data.append({
                'time': current_time.strftime('%H:%M'),
                'flow': max(100, flow),  # 确保最小值
                'timestamp': current_time.isoformat()
            })
            current_time += datetime.timedelta(hours=1)
            
        return data
    
    def generate_speed_data(self):
        """生成车速分布数据"""
        speed_ranges = ['0-20', '20-40', '40-60', '60-80', '80+']
        data = []
        
        for speed_range in speed_ranges:
            if speed_range == '40-60':  # 正常车速范围车辆最多
                count = random.randint(280, 350)
            elif speed_range in ['20-40', '60-80']:
                count = random.randint(150, 250)
            else:
                count = random.randint(30, 100)
                
            data.append({
                'speed_range': speed_range,
                'count': count
            })
            
        return data
    
    def generate_vehicle_types_data(self):
        """生成车辆类型统计数据"""
        vehicle_types = [
            {'type': '小型车', 'count': random.randint(700, 900), 'color': '#00d4ff'},
            {'type': '中型车', 'count': random.randint(200, 350), 'color': '#00ff88'},
            {'type': '大型车', 'count': random.randint(100, 200), 'color': '#ffd700'},
            {'type': '货车', 'count': random.randint(50, 120), 'color': '#ff6b6b'},
            {'type': '客车', 'count': random.randint(20, 60), 'color': '#9c88ff'}
        ]
        return vehicle_types
    
    def generate_violation_data(self):
        """生成违法行为统计数据"""
        violations = [
            {'type': '违法停车', 'count': random.randint(30, 60)},
            {'type': '超速行驶', 'count': random.randint(10, 30)},
            {'type': '闯红灯', 'count': random.randint(5, 15)},
            {'type': '逆向行驶', 'count': random.randint(1, 8)},
            {'type': '不按道行驶', 'count': random.randint(8, 20)}
        ]
        return violations
    
    def generate_realtime_data(self):
        """生成实时数据"""
        return {
            'current_traffic': random.randint(1000, 1500),
            'average_speed': random.randint(65, 85),
            'road_occupancy': round(random.uniform(15.0, 35.0), 1),
            'incident_count': random.randint(0, 5),
            'timestamp': datetime.datetime.now().isoformat()
        }
    
    def generate_events(self, count=10):
        """生成事件列表"""
        event_types = [
            {'type': 'normal', 'desc': '车辆正常通过'},
            {'type': 'normal', 'desc': '信号灯切换正常'},
            {'type': 'warning', 'desc': '检测到超速车辆'},
            {'type': 'warning', 'desc': '车流量异常增加'},
            {'type': 'urgent', 'desc': '发现交通事故'},
            {'type': 'urgent', 'desc': '违法停车'},
        ]
        
        locations = ['东西向主道', '南北向辅道', '路口东南角', '主路口', '全路段']
        events = []
        
        for i in range(count):
            event = random.choice(event_types)
            location = random.choice(locations)
            time_offset = random.randint(0, 3600)  # 最近1小时内
            event_time = datetime.datetime.now() - datetime.timedelta(seconds=time_offset)
            
            events.append({
                'id': i + 1,
                'type': event['type'],
                'description': event['desc'],
                'location': location,
                'time': event_time.strftime('%H:%M:%S'),
                'timestamp': event_time.isoformat()
            })
            
        return sorted(events, key=lambda x: x['timestamp'], reverse=True)

# 创建数据生成器实例
data_generator = TrafficDataGenerator()

# API路由定义
@app.route('/')
def index():
    """API首页"""
    return jsonify({
        'message': '智慧城市交通流量可视化数据分析系统 API',
        'version': '1.0.0',
        'endpoints': {
            '/api/traffic/flow': '获取车流量数据',
            '/api/traffic/speed': '获取车速分布数据',
            '/api/traffic/vehicles': '获取车辆类型统计',
            '/api/traffic/violations': '获取违法行为统计',
            '/api/traffic/realtime': '获取实时数据',
            '/api/traffic/events': '获取事件列表'
        }
    })

@app.route('/api/traffic/flow')
def get_traffic_flow():
    """获取车流量数据"""
    hours = request.args.get('hours', 24, type=int)
    data = data_generator.generate_traffic_flow_data(hours)
    
    return jsonify({
        'success': True,
        'data': data,
        'total': len(data),
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/speed')
def get_speed_data():
    """获取车速分布数据"""
    data = data_generator.generate_speed_data()
    
    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/vehicles')
def get_vehicle_types():
    """获取车辆类型统计"""
    data = data_generator.generate_vehicle_types_data()
    
    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/violations')
def get_violations():
    """获取违法行为统计"""
    data = data_generator.generate_violation_data()
    
    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/realtime')
def get_realtime_data():
    """获取实时数据"""
    data = data_generator.generate_realtime_data()
    
    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/events')
def get_events():
    """获取事件列表"""
    count = request.args.get('count', 10, type=int)
    data = data_generator.generate_events(count)
    
    return jsonify({
        'success': True,
        'data': data,
        'total': len(data),
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/intersection/<intersection_id>')
def get_intersection_data(intersection_id):
    """获取特定路口数据"""
    # 模拟路口数据
    data = {
        'intersection_id': intersection_id,
        'name': f'路口{intersection_id}',
        'traffic_light_status': {
            'current_phase': random.choice(['red', 'yellow', 'green']),
            'remaining_time': random.randint(5, 30)
        },
        'vehicle_count': {
            'east_west': random.randint(5, 15),
            'north_south': random.randint(3, 12)
        },
        'average_speed': random.randint(25, 45),
        'incidents': random.randint(0, 3)
    }
    
    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': 'API endpoint not found',
        'message': '请求的API接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': '服务器内部错误'
    }), 500

if __name__ == '__main__':
    print("启动智慧城市交通流量可视化数据分析系统后端服务...")
    print("API文档: http://localhost:5000")
    print("车流量数据: http://localhost:5000/api/traffic/flow")
    print("实时数据: http://localhost:5000/api/traffic/realtime")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
