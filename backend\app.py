#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧城市交通流量可视化数据分析系统 - 后端API
提供交通数据的RESTful API接口，集成数据库和网络爬虫
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
import random
import datetime
import json
import os
import logging
from datetime import timedelta
from sqlalchemy import func, and_, or_, desc

# 导入配置和模型
from config import config
from models import db, TrafficFlow, VehicleSpeed, TrafficEvent, DeviceStatus, ViolationRecord, IntersectionStatus

# 导入爬虫模块
from data_crawler import crawler
from web_scraper import web_scraper

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(config_name='development'):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 创建日志目录
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 初始化扩展
    db.init_app(app)
    CORS(app)  # 允许跨域请求

    # 初始化爬虫
    crawler.init_app(app)

    return app

app = create_app()

# 数据库服务类
class DatabaseService:
    """数据库服务类"""

    @staticmethod
    def get_traffic_flow_data(hours=24, location_id=None):
        """从数据库获取车流量数据"""
        try:
            query = TrafficFlow.query

            # 时间范围过滤
            start_time = datetime.datetime.utcnow() - timedelta(hours=hours)
            query = query.filter(TrafficFlow.record_time >= start_time)

            # 位置过滤
            if location_id:
                query = query.filter(TrafficFlow.location_id == location_id)

            # 按时间排序
            traffic_data = query.order_by(TrafficFlow.record_time.desc()).limit(1000).all()

            # 转换为字典格式
            result = []
            for item in traffic_data:
                result.append({
                    'time': item.record_time.strftime('%H:%M') if item.record_time else '',
                    'flow': item.flow_count,
                    'location': item.location_name,
                    'vehicle_type': item.vehicle_type,
                    'direction': item.direction,
                    'timestamp': item.record_time.isoformat() if item.record_time else ''
                })

            return result

        except Exception as e:
            logger.error(f"获取车流量数据失败: {e}")
            return []

    @staticmethod
    def get_speed_distribution_data():
        """获取车速分布数据"""
        try:
            # 获取最近1小时的车速数据
            start_time = datetime.datetime.utcnow() - timedelta(hours=1)
            speed_data = VehicleSpeed.query.filter(
                VehicleSpeed.record_time >= start_time
            ).all()

            # 统计不同速度区间的车辆数量
            speed_ranges = {
                '0-20': 0, '20-40': 0, '40-60': 0, '60-80': 0, '80+': 0
            }

            for record in speed_data:
                speed = record.speed
                if speed <= 20:
                    speed_ranges['0-20'] += 1
                elif speed <= 40:
                    speed_ranges['20-40'] += 1
                elif speed <= 60:
                    speed_ranges['40-60'] += 1
                elif speed <= 80:
                    speed_ranges['60-80'] += 1
                else:
                    speed_ranges['80+'] += 1

            result = []
            for range_name, count in speed_ranges.items():
                result.append({
                    'speed_range': range_name,
                    'count': count
                })

            return result

        except Exception as e:
            logger.error(f"获取车速分布数据失败: {e}")
            return []

    @staticmethod
    def get_vehicle_types_data():
        """获取车辆类型统计数据"""
        try:
            # 获取最近24小时的数据
            start_time = datetime.datetime.utcnow() - timedelta(hours=24)

            # 按车辆类型统计
            vehicle_stats = db.session.query(
                TrafficFlow.vehicle_type,
                func.sum(TrafficFlow.flow_count).label('total_count')
            ).filter(
                TrafficFlow.record_time >= start_time
            ).group_by(TrafficFlow.vehicle_type).all()

            # 颜色映射
            color_map = {
                '小型车': '#00d4ff',
                '中型车': '#00ff88',
                '大型车': '#ffd700',
                '货车': '#ff6b6b',
                '客车': '#9c88ff',
                'all': '#64b5f6'
            }

            result = []
            for vehicle_type, count in vehicle_stats:
                result.append({
                    'type': vehicle_type,
                    'count': int(count) if count else 0,
                    'color': color_map.get(vehicle_type, '#cccccc')
                })

            return result

        except Exception as e:
            logger.error(f"获取车辆类型数据失败: {e}")
            return []

    @staticmethod
    def get_violation_data():
        """获取违法行为统计数据"""
        try:
            # 获取最近7天的违法记录
            start_time = datetime.datetime.utcnow() - timedelta(days=7)

            # 按违法类型统计
            violation_stats = db.session.query(
                ViolationRecord.violation_type,
                func.count(ViolationRecord.id).label('count')
            ).filter(
                ViolationRecord.record_time >= start_time
            ).group_by(ViolationRecord.violation_type).all()

            result = []
            for violation_type, count in violation_stats:
                result.append({
                    'type': violation_type,
                    'count': int(count)
                })

            return result

        except Exception as e:
            logger.error(f"获取违法行为数据失败: {e}")
            return []

    @staticmethod
    def get_realtime_data():
        """获取实时数据"""
        try:
            # 获取最新的车流量数据
            latest_flow = TrafficFlow.query.order_by(
                TrafficFlow.record_time.desc()
            ).first()

            # 获取最新的车速数据
            latest_speeds = VehicleSpeed.query.filter(
                VehicleSpeed.record_time >= datetime.datetime.utcnow() - timedelta(minutes=10)
            ).all()

            # 计算平均车速
            avg_speed = 0
            if latest_speeds:
                total_speed = sum(record.speed for record in latest_speeds)
                avg_speed = round(total_speed / len(latest_speeds), 1)

            # 获取活跃事件数量
            active_incidents = TrafficEvent.query.filter(
                TrafficEvent.status == 'active'
            ).count()

            # 计算道路占有率（模拟）
            road_occupancy = random.uniform(15.0, 35.0)

            return {
                'current_traffic': latest_flow.flow_count if latest_flow else 0,
                'average_speed': avg_speed,
                'road_occupancy': round(road_occupancy, 1),
                'incident_count': active_incidents,
                'timestamp': datetime.datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return {
                'current_traffic': 0,
                'average_speed': 0,
                'road_occupancy': 0.0,
                'incident_count': 0,
                'timestamp': datetime.datetime.utcnow().isoformat()
            }

    @staticmethod
    def get_events_data(count=10):
        """获取事件列表数据"""
        try:
            # 获取最新的事件
            events = TrafficEvent.query.order_by(
                TrafficEvent.start_time.desc()
            ).limit(count).all()

            result = []
            for event in events:
                result.append({
                    'id': event.id,
                    'type': event.event_level,
                    'description': event.description,
                    'location': event.location_name,
                    'time': event.start_time.strftime('%H:%M:%S') if event.start_time else '',
                    'timestamp': event.start_time.isoformat() if event.start_time else ''
                })

            return result

        except Exception as e:
            logger.error(f"获取事件数据失败: {e}")
            return []

# 模拟数据生成器（作为备用）
class TrafficDataGenerator:
    """交通数据生成器（备用）"""

    def __init__(self):
        self.base_traffic_flow = 1200  # 基础车流量
        self.base_speed = 75  # 基础车速
        
    def generate_traffic_flow_data(self, hours=24):
        """生成车流量数据"""
        data = []
        current_time = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        for i in range(hours):
            # 模拟一天中不同时段的车流量变化
            hour = current_time.hour
            if 6 <= hour <= 9 or 17 <= hour <= 20:  # 高峰期
                flow = self.base_traffic_flow + random.randint(200, 500)
            elif 22 <= hour or hour <= 5:  # 夜间
                flow = self.base_traffic_flow - random.randint(300, 600)
            else:  # 平峰期
                flow = self.base_traffic_flow + random.randint(-100, 200)
                
            data.append({
                'time': current_time.strftime('%H:%M'),
                'flow': max(100, flow),  # 确保最小值
                'timestamp': current_time.isoformat()
            })
            current_time += datetime.timedelta(hours=1)
            
        return data
    
    def generate_speed_data(self):
        """生成车速分布数据"""
        speed_ranges = ['0-20', '20-40', '40-60', '60-80', '80+']
        data = []
        
        for speed_range in speed_ranges:
            if speed_range == '40-60':  # 正常车速范围车辆最多
                count = random.randint(280, 350)
            elif speed_range in ['20-40', '60-80']:
                count = random.randint(150, 250)
            else:
                count = random.randint(30, 100)
                
            data.append({
                'speed_range': speed_range,
                'count': count
            })
            
        return data
    
    def generate_vehicle_types_data(self):
        """生成车辆类型统计数据"""
        vehicle_types = [
            {'type': '小型车', 'count': random.randint(700, 900), 'color': '#00d4ff'},
            {'type': '中型车', 'count': random.randint(200, 350), 'color': '#00ff88'},
            {'type': '大型车', 'count': random.randint(100, 200), 'color': '#ffd700'},
            {'type': '货车', 'count': random.randint(50, 120), 'color': '#ff6b6b'},
            {'type': '客车', 'count': random.randint(20, 60), 'color': '#9c88ff'}
        ]
        return vehicle_types
    
    def generate_violation_data(self):
        """生成违法行为统计数据"""
        violations = [
            {'type': '违法停车', 'count': random.randint(30, 60)},
            {'type': '超速行驶', 'count': random.randint(10, 30)},
            {'type': '闯红灯', 'count': random.randint(5, 15)},
            {'type': '逆向行驶', 'count': random.randint(1, 8)},
            {'type': '不按道行驶', 'count': random.randint(8, 20)}
        ]
        return violations
    
    def generate_realtime_data(self):
        """生成实时数据"""
        return {
            'current_traffic': random.randint(1000, 1500),
            'average_speed': random.randint(65, 85),
            'road_occupancy': round(random.uniform(15.0, 35.0), 1),
            'incident_count': random.randint(0, 5),
            'timestamp': datetime.datetime.now().isoformat()
        }
    
    def generate_events(self, count=10):
        """生成事件列表"""
        event_types = [
            {'type': 'normal', 'desc': '车辆正常通过'},
            {'type': 'normal', 'desc': '信号灯切换正常'},
            {'type': 'warning', 'desc': '检测到超速车辆'},
            {'type': 'warning', 'desc': '车流量异常增加'},
            {'type': 'urgent', 'desc': '发现交通事故'},
            {'type': 'urgent', 'desc': '违法停车'},
        ]
        
        locations = ['东西向主道', '南北向辅道', '路口东南角', '主路口', '全路段']
        events = []
        
        for i in range(count):
            event = random.choice(event_types)
            location = random.choice(locations)
            time_offset = random.randint(0, 3600)  # 最近1小时内
            event_time = datetime.datetime.now() - datetime.timedelta(seconds=time_offset)
            
            events.append({
                'id': i + 1,
                'type': event['type'],
                'description': event['desc'],
                'location': location,
                'time': event_time.strftime('%H:%M:%S'),
                'timestamp': event_time.isoformat()
            })
            
        return sorted(events, key=lambda x: x['timestamp'], reverse=True)

# 创建数据生成器实例
data_generator = TrafficDataGenerator()

# API路由定义
@app.route('/')
def index():
    """API首页"""
    return jsonify({
        'message': '智慧城市交通流量可视化数据分析系统 API',
        'version': '1.0.0',
        'endpoints': {
            '/api/traffic/flow': '获取车流量数据',
            '/api/traffic/speed': '获取车速分布数据',
            '/api/traffic/vehicles': '获取车辆类型统计',
            '/api/traffic/violations': '获取违法行为统计',
            '/api/traffic/realtime': '获取实时数据',
            '/api/traffic/events': '获取事件列表'
        }
    })

@app.route('/api/traffic/flow')
def get_traffic_flow():
    """获取车流量数据"""
    hours = request.args.get('hours', 24, type=int)
    location_id = request.args.get('location_id', None)

    # 优先从数据库获取数据
    try:
        data = DatabaseService.get_traffic_flow_data(hours, location_id)
        if not data:
            # 如果数据库没有数据，使用模拟数据
            data = data_generator.generate_traffic_flow_data(hours)
            logger.info("使用模拟车流量数据")
        else:
            logger.info(f"从数据库获取车流量数据 {len(data)} 条")
    except Exception as e:
        logger.error(f"获取车流量数据失败: {e}")
        data = data_generator.generate_traffic_flow_data(hours)

    return jsonify({
        'success': True,
        'data': data,
        'total': len(data),
        'timestamp': datetime.datetime.now().isoformat(),
        'source': 'database' if data and 'timestamp' in str(data[0]) else 'mock'
    })

@app.route('/api/traffic/speed')
def get_speed_data():
    """获取车速分布数据"""
    try:
        data = DatabaseService.get_speed_distribution_data()
        if not data:
            data = data_generator.generate_speed_data()
            logger.info("使用模拟车速数据")
        else:
            logger.info(f"从数据库获取车速数据 {len(data)} 条")
    except Exception as e:
        logger.error(f"获取车速数据失败: {e}")
        data = data_generator.generate_speed_data()

    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/vehicles')
def get_vehicle_types():
    """获取车辆类型统计"""
    try:
        data = DatabaseService.get_vehicle_types_data()
        if not data:
            data = data_generator.generate_vehicle_types_data()
            logger.info("使用模拟车辆类型数据")
        else:
            logger.info(f"从数据库获取车辆类型数据 {len(data)} 条")
    except Exception as e:
        logger.error(f"获取车辆类型数据失败: {e}")
        data = data_generator.generate_vehicle_types_data()

    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/violations')
def get_violations():
    """获取违法行为统计"""
    try:
        data = DatabaseService.get_violation_data()
        if not data:
            data = data_generator.generate_violation_data()
            logger.info("使用模拟违法数据")
        else:
            logger.info(f"从数据库获取违法数据 {len(data)} 条")
    except Exception as e:
        logger.error(f"获取违法数据失败: {e}")
        data = data_generator.generate_violation_data()

    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/realtime')
def get_realtime_data():
    """获取实时数据"""
    try:
        data = DatabaseService.get_realtime_data()
        logger.info("从数据库获取实时数据")
    except Exception as e:
        logger.error(f"获取实时数据失败: {e}")
        data = data_generator.generate_realtime_data()

    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/events')
def get_events():
    """获取事件列表"""
    count = request.args.get('count', 10, type=int)

    try:
        data = DatabaseService.get_events_data(count)
        if not data:
            data = data_generator.generate_events(count)
            logger.info("使用模拟事件数据")
        else:
            logger.info(f"从数据库获取事件数据 {len(data)} 条")
    except Exception as e:
        logger.error(f"获取事件数据失败: {e}")
        data = data_generator.generate_events(count)

    return jsonify({
        'success': True,
        'data': data,
        'total': len(data),
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/api/traffic/intersection/<intersection_id>')
def get_intersection_data(intersection_id):
    """获取特定路口数据"""
    try:
        # 从数据库获取路口数据
        intersection = IntersectionStatus.query.filter_by(
            intersection_id=intersection_id
        ).first()

        if intersection:
            data = intersection.to_dict()
            logger.info(f"从数据库获取路口 {intersection_id} 数据")
        else:
            # 模拟路口数据
            data = {
                'intersection_id': intersection_id,
                'intersection_name': f'路口{intersection_id}',
                'traffic_light_status': {
                    'current_phase': random.choice(['red', 'yellow', 'green']),
                    'remaining_time': random.randint(5, 30)
                },
                'vehicle_count': {
                    'east_west': random.randint(5, 15),
                    'north_south': random.randint(3, 12)
                },
                'average_speed': random.randint(25, 45),
                'incident_count': random.randint(0, 3)
            }
            logger.info(f"使用模拟路口 {intersection_id} 数据")

    except Exception as e:
        logger.error(f"获取路口数据失败: {e}")
        data = {
            'intersection_id': intersection_id,
            'intersection_name': f'路口{intersection_id}',
            'traffic_light_status': {
                'current_phase': 'red',
                'remaining_time': 30
            },
            'vehicle_count': {
                'east_west': 0,
                'north_south': 0
            },
            'average_speed': 0,
            'incident_count': 0
        }

    return jsonify({
        'success': True,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    })

# 爬虫控制API
@app.route('/api/crawler/start', methods=['POST'])
def start_crawler():
    """启动数据爬虫"""
    try:
        crawler.start_crawling()
        return jsonify({
            'success': True,
            'message': '数据爬虫已启动',
            'timestamp': datetime.datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"启动爬虫失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '启动数据爬虫失败'
        }), 500

@app.route('/api/crawler/stop', methods=['POST'])
def stop_crawler():
    """停止数据爬虫"""
    try:
        crawler.stop_crawling()
        return jsonify({
            'success': True,
            'message': '数据爬虫已停止',
            'timestamp': datetime.datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"停止爬虫失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '停止数据爬虫失败'
        }), 500

@app.route('/api/crawler/status')
def get_crawler_status():
    """获取爬虫状态"""
    try:
        status = {
            'is_running': crawler.is_running,
            'active_threads': len(crawler.threads),
            'queue_size': crawler.data_queue.qsize(),
            'timestamp': datetime.datetime.now().isoformat()
        }

        return jsonify({
            'success': True,
            'data': status,
            'timestamp': datetime.datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取爬虫状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取爬虫状态失败'
        }), 500

# 网络爬取API
@app.route('/api/scraper/run', methods=['POST'])
def run_web_scraper():
    """运行网络数据爬取"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        scrape_type = data.get('type', 'all')

        results = []

        if scrape_type in ['all', 'government']:
            gov_data = web_scraper.scrape_government_traffic_data()
            results.extend(gov_data)

        if scrape_type in ['all', 'news']:
            news_data = web_scraper.scrape_news_traffic_data()
            results.extend(news_data)

        if scrape_type in ['all', 'social']:
            social_data = web_scraper.scrape_social_media_traffic_data()
            results.extend(social_data)

        if scrape_type in ['all', 'weather']:
            weather_data = web_scraper.scrape_weather_traffic_impact()
            results.extend(weather_data)

        if scrape_type in ['all', 'api']:
            api_data = web_scraper.scrape_real_time_traffic_apis()
            results.extend(api_data)

        return jsonify({
            'success': True,
            'message': f'网络爬取完成，获取 {len(results)} 条数据',
            'data': results[:10],  # 只返回前10条作为示例
            'total': len(results),
            'timestamp': datetime.datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"网络爬取失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '网络数据爬取失败'
        }), 500

# 数据库管理API
@app.route('/api/database/init', methods=['POST'])
def init_database():
    """初始化数据库"""
    try:
        # 创建所有表
        db.create_all()

        return jsonify({
            'success': True,
            'message': '数据库初始化成功',
            'timestamp': datetime.datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '数据库初始化失败'
        }), 500

@app.route('/api/database/stats')
def get_database_stats():
    """获取数据库统计信息"""
    try:
        stats = {
            'traffic_flow_count': TrafficFlow.query.count(),
            'vehicle_speed_count': VehicleSpeed.query.count(),
            'traffic_event_count': TrafficEvent.query.count(),
            'device_status_count': DeviceStatus.query.count(),
            'violation_record_count': ViolationRecord.query.count(),
            'intersection_status_count': IntersectionStatus.query.count(),
            'timestamp': datetime.datetime.now().isoformat()
        }

        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取数据库统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取数据库统计失败'
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': 'API endpoint not found',
        'message': '请求的API接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': '服务器内部错误'
    }), 500

if __name__ == '__main__':
    print("启动智慧城市交通流量可视化数据分析系统后端服务...")
    print("API文档: http://localhost:5000")
    print("车流量数据: http://localhost:5000/api/traffic/flow")
    print("实时数据: http://localhost:5000/api/traffic/realtime")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
