@echo off
chcp 65001
echo ========================================
echo 智慧城市交通流量可视化数据分析系统
echo ========================================
echo.

echo 🔧 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.7+并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 检查MySQL服务...
net start | find "MySQL" >nul
if %errorlevel% neq 0 (
    echo ⚠️ MySQL服务未启动，尝试启动...
    net start mysql >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ MySQL服务启动失败
        echo 请确保MySQL已安装并配置正确
        echo 数据库密码设置为: root
        pause
        exit /b 1
    )
)

echo ✅ MySQL服务运行正常
echo.

echo 📚 安装Python依赖包...
cd backend
pip install -r requirements.txt >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 依赖包安装可能有问题，继续启动...
)
echo ✅ 依赖包检查完成
echo.

echo 🚀 启动后端API服务（包含数据库和爬虫）...
start "智慧交通后端服务" cmd /k "python run_server.py"
cd ..

echo.
echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo.
echo 🌐 启动前端服务...
cd frontend
start "智慧交通前端服务" cmd /k "python -m http.server 8080"
cd ..

echo.
echo ========================================
echo 🎉 服务启动完成！
echo ========================================
echo.
echo 📱 前端访问地址:
echo    导航页面: http://localhost:8080/navigation.html
echo    主界面: http://localhost:8080/index.html
echo    路口监控: http://localhost:8080/intersection.html
echo.
echo 🔗 后端API地址:
echo    API首页: http://localhost:5000
echo    车流量数据: http://localhost:5000/api/traffic/flow
echo    实时数据: http://localhost:5000/api/traffic/realtime
echo    爬虫状态: http://localhost:5000/api/crawler/status
echo    数据库统计: http://localhost:5000/api/database/stats
echo.
echo 🕷️ 数据爬虫功能:
echo    - 自动从网络爬取交通数据
echo    - 存储到MySQL数据库
echo    - 提供实时数据更新
echo.
echo 💾 数据库信息:
echo    主机: localhost:3306
echo    数据库: smart_traffic
echo    用户: root
echo    密码: root
echo.
echo ========================================
echo 💡 使用提示:
echo 1. 打开浏览器访问导航页面开始使用
echo 2. 后端会自动创建数据库和示例数据
echo 3. 数据爬虫会自动运行并更新数据
echo 4. 关闭窗口即可停止相应服务
echo ========================================
echo.
echo 按任意键退出...
pause >nul
