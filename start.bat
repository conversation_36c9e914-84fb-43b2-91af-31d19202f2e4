@echo off
chcp 65001
echo ========================================
echo 智慧城市交通流量可视化数据分析系统
echo ========================================
echo.

echo 正在启动后端服务...
cd backend
start "后端API服务" cmd /k "python app.py"
cd ..

echo.
echo 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo.
echo 正在启动前端服务...
cd frontend
start "前端服务" cmd /k "python -m http.server 8080"
cd ..

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo 前端地址: http://localhost:8080
echo 主界面: http://localhost:8080/index.html
echo 路口监控: http://localhost:8080/intersection.html
echo 后端API: http://localhost:5000
echo ========================================
echo.
echo 按任意键退出...
pause >nul
