@echo off
chcp 65001
echo ========================================
echo 智慧城市交通流量可视化系统 - 直接访问
echo ========================================
echo.

echo 🌐 正在打开系统界面...
echo.

REM 获取当前目录的完整路径
set "CURRENT_DIR=%~dp0"

echo 📱 打开导航页面...
start "" "%CURRENT_DIR%frontend\navigation.html"

timeout /t 2 /nobreak >nul

echo 📱 打开主界面...
start "" "%CURRENT_DIR%frontend\index.html"

timeout /t 2 /nobreak >nul

echo 📱 打开路口监控界面...
start "" "%CURRENT_DIR%frontend\intersection.html"

echo.
echo ========================================
echo 🎉 界面已在浏览器中打开！
echo ========================================
echo.
echo 💡 说明:
echo - 直接在浏览器中打开HTML文件
echo - 使用模拟数据，功能完全可用
echo - 无需安装任何软件
echo - 所有图表和3D场景都正常工作
echo.
echo 📱 如果浏览器未自动打开，请手动打开:
echo    导航页面: %CURRENT_DIR%frontend\navigation.html
echo    主界面: %CURRENT_DIR%frontend\index.html
echo    路口监控: %CURRENT_DIR%frontend\intersection.html
echo.
echo ========================================
echo.
pause
