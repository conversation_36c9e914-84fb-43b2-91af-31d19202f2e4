# 智慧城市交通流量可视化数据分析系统 - 使用说明

## 项目概述

本项目是一个基于Web技术的智慧城市交通流量可视化数据分析系统，包含两个主要界面：

1. **智慧交通运营管理平台** (`index.html`) - 综合性交通监控大屏
2. **交通监控平台** (`intersection.html`) - 路口专项监控平台

## 项目结构

```
智慧城市交通流量/
├── frontend/                 # 前端文件
│   ├── index.html           # 主界面 - 智慧交通运营管理平台
│   ├── intersection.html    # 路口监控界面
│   ├── navigation.html      # 导航页面
│   ├── styles/              # CSS样式文件
│   │   ├── main.css        # 主界面样式
│   │   └── intersection.css # 路口界面样式
│   └── js/                  # JavaScript文件
│       ├── main.js         # 主界面逻辑
│       ├── charts.js       # 图表管理
│       ├── scene.js        # 3D场景
│       ├── intersection-*.js # 路口界面相关
├── backend/                 # 后端API服务
│   ├── app.py              # Flask API服务器
│   └── requirements.txt    # Python依赖
├── README.md               # 项目说明
└── 使用说明.md             # 本文件
```

## 快速开始

### 方法一：直接打开HTML文件（推荐）

1. **打开导航页面**
   - 双击 `frontend/navigation.html` 文件
   - 或在浏览器中打开该文件

2. **选择界面**
   - 点击"智慧交通运营管理平台"进入主界面
   - 点击"交通监控平台"进入路口监控界面

### 方法二：使用Web服务器

如果您的系统已安装Python：

1. **启动前端服务**
   ```bash
   cd frontend
   python -m http.server 8080
   ```

2. **启动后端API服务**
   ```bash
   cd backend
   pip install -r requirements.txt
   python app.py
   ```

3. **访问系统**
   - 导航页面: http://localhost:8080/navigation.html
   - 主界面: http://localhost:8080/index.html
   - 路口监控: http://localhost:8080/intersection.html
   - API服务: http://localhost:5000

## 功能特性

### 智慧交通运营管理平台 (index.html)

**界面布局：**
- 顶部：导航栏，包含系统标题、菜单和时间显示
- 左侧：路口监控视频、车流量趋势图、通行速度统计
- 中央：3D道路场景，显示车辆轨迹和事件标记
- 右侧：路段监控视频、车流量统计图、实时数据面板

**核心功能：**
- ✅ 实时车流量监控和趋势分析
- ✅ 3D道路场景可视化
- ✅ 车辆轨迹实时追踪
- ✅ 智能事件预警系统
- ✅ 多维度数据统计展示
- ✅ 响应式界面设计

### 交通监控平台 (intersection.html)

**界面布局：**
- 顶部：平台标题和时间显示
- 左侧：车流量统计、车速分析、违法行为统计
- 中央：3D路口场景，信号灯控制，统计数据
- 右侧：设备状态监控、实时事件列表、控制操作

**核心功能：**
- ✅ 路口3D可视化展示
- ✅ 信号灯智能控制系统
- ✅ 违法行为实时检测
- ✅ 事件处理和响应
- ✅ 设备状态实时监控
- ✅ 交互式控制面板

## 技术特点

### 前端技术
- **HTML5 + CSS3 + JavaScript** - 现代Web标准
- **ECharts** - 专业数据可视化图表库
- **Three.js** - 3D场景渲染引擎
- **响应式设计** - 适配不同屏幕尺寸
- **模块化架构** - 代码结构清晰，易于维护

### 后端技术
- **Python Flask** - 轻量级Web框架
- **RESTful API** - 标准化接口设计
- **实时数据生成** - 模拟真实交通数据
- **跨域支持** - 前后端分离架构

### 视觉设计
- **深蓝色科技风格** - 专业的监控大屏效果
- **渐变背景和光效** - 增强视觉层次感
- **动画过渡效果** - 提升用户体验
- **数据实时更新** - 动态展示最新信息

## 数据说明

系统使用模拟数据，包括：

- **车流量数据** - 按时段变化的车辆通行量
- **车速分布** - 不同速度区间的车辆统计
- **车辆类型** - 小型车、中型车、大型车等分类
- **违法行为** - 超速、闯红灯、违停等统计
- **实时事件** - 交通事故、设备故障等
- **设备状态** - 摄像头、信号灯、检测器状态

## 浏览器兼容性

推荐使用以下现代浏览器：
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 常见问题

### Q: 3D场景无法显示？
A: 请确保浏览器支持WebGL，并启用硬件加速。

### Q: 图表显示异常？
A: 请检查网络连接，确保ECharts库正常加载。

### Q: 数据不更新？
A: 系统使用模拟数据，会自动更新。如需真实数据，请连接后端API。

### Q: 页面加载缓慢？
A: 建议使用本地Web服务器而非直接打开文件。

## 扩展开发

### 添加新功能
1. 在相应的JavaScript文件中添加功能代码
2. 在CSS文件中添加样式定义
3. 在HTML文件中添加界面元素

### 连接真实数据
1. 修改API接口地址
2. 调整数据格式适配
3. 添加数据验证和错误处理

### 自定义样式
1. 修改CSS变量定义
2. 调整颜色主题
3. 更新动画效果

## 联系支持

如有问题或建议，请：
1. 查看项目文档
2. 检查浏览器控制台错误信息
3. 确认所有文件完整性

---

**版本信息：** v1.0.0  
**更新时间：** 2024年6月  
**技术支持：** 智慧城市交通流量可视化数据分析系统开发团队
