# -*- coding: utf-8 -*-
"""
网络数据爬取模块
从各种网络数据源爬取交通相关数据
"""

import requests
import json
import time
import random
import logging
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin, urlparse
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional

from crawler_config import CrawlerConfig
from models import db, TrafficFlow, VehicleSpeed, TrafficEvent, DeviceStatus, ViolationRecord

logger = logging.getLogger(__name__)

class WebScraper:
    """网络数据爬取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(CrawlerConfig.DEFAULT_HEADERS)
        
    async def scrape_traffic_data_async(self, urls: List[str]) -> List[Dict]:
        """异步爬取多个URL的交通数据"""
        async with aiohttp.ClientSession() as session:
            tasks = [self._fetch_url_async(session, url) for url in urls]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            valid_results = []
            for result in results:
                if isinstance(result, dict):
                    valid_results.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"异步请求失败: {result}")
                    
            return valid_results
            
    async def _fetch_url_async(self, session: aiohttp.ClientSession, url: str) -> Dict:
        """异步获取单个URL数据"""
        try:
            async with session.get(url, timeout=30) as response:
                if response.status == 200:
                    content = await response.text()
                    return self._parse_traffic_data(content, url)
                else:
                    logger.warning(f"HTTP {response.status}: {url}")
                    return {}
        except Exception as e:
            logger.error(f"异步请求失败 {url}: {e}")
            return {}
            
    def scrape_government_traffic_data(self) -> List[Dict]:
        """爬取政府交通数据网站"""
        traffic_data = []
        
        # 模拟政府交通数据网站
        government_sites = [
            {
                'name': '交通运输部',
                'url': 'http://www.mot.gov.cn/',
                'parser': self._parse_mot_data
            },
            {
                'name': '北京交通委',
                'url': 'http://jtw.beijing.gov.cn/',
                'parser': self._parse_beijing_traffic_data
            },
            {
                'name': '上海交通委',
                'url': 'http://jtw.sh.gov.cn/',
                'parser': self._parse_shanghai_traffic_data
            }
        ]
        
        for site in government_sites:
            try:
                logger.info(f"🌐 爬取 {site['name']} 数据")
                data = self._scrape_website(site['url'], site['parser'])
                if data:
                    traffic_data.extend(data)
                    logger.info(f"✅ 从 {site['name']} 获取 {len(data)} 条数据")
                    
                # 避免请求过于频繁
                time.sleep(CrawlerConfig.REQUEST_DELAY)
                
            except Exception as e:
                logger.error(f"❌ 爬取 {site['name']} 失败: {e}")
                
        return traffic_data
        
    def scrape_news_traffic_data(self) -> List[Dict]:
        """爬取新闻网站的交通数据"""
        traffic_events = []
        
        # 新闻网站列表
        news_sites = [
            {
                'name': '新华网交通',
                'url': 'http://www.xinhuanet.com/auto/',
                'keywords': ['交通事故', '道路拥堵', '交通管制', '高速公路']
            },
            {
                'name': '人民网交通',
                'url': 'http://auto.people.com.cn/',
                'keywords': ['交通', '道路', '车辆', '高速']
            }
        ]
        
        for site in news_sites:
            try:
                logger.info(f"📰 爬取 {site['name']} 交通新闻")
                events = self._scrape_traffic_news(site['url'], site['keywords'])
                if events:
                    traffic_events.extend(events)
                    logger.info(f"✅ 从 {site['name']} 获取 {len(events)} 条交通事件")
                    
                time.sleep(CrawlerConfig.REQUEST_DELAY)
                
            except Exception as e:
                logger.error(f"❌ 爬取 {site['name']} 失败: {e}")
                
        return traffic_events
        
    def scrape_social_media_traffic_data(self) -> List[Dict]:
        """爬取社交媒体的交通数据"""
        social_data = []
        
        try:
            # 模拟从社交媒体API获取交通相关数据
            # 这里使用公开API模拟社交媒体数据
            
            # 使用JSONPlaceholder模拟社交媒体数据
            response = self.session.get('https://jsonplaceholder.typicode.com/posts')
            if response.status_code == 200:
                posts = response.json()
                
                # 筛选包含交通关键词的帖子
                traffic_keywords = ['traffic', 'road', 'highway', 'accident', 'congestion', '交通', '道路', '拥堵']
                
                for post in posts:
                    title = post.get('title', '').lower()
                    body = post.get('body', '').lower()
                    
                    # 检查是否包含交通关键词
                    if any(keyword in title or keyword in body for keyword in traffic_keywords):
                        event_data = {
                            'source': 'social_media',
                            'event_type': 'social_report',
                            'event_level': 'normal',
                            'location_id': f'LOC{random.randint(1, 4):03d}',
                            'location_name': f'社交媒体报告位置{random.randint(1, 10)}',
                            'description': post['title'][:100],
                            'start_time': datetime.utcnow() - timedelta(hours=random.randint(1, 24)),
                            'status': 'reported',
                            'confidence': random.uniform(0.3, 0.7)  # 社交媒体数据置信度较低
                        }
                        social_data.append(event_data)
                        
                logger.info(f"✅ 从社交媒体获取 {len(social_data)} 条交通报告")
                
        except Exception as e:
            logger.error(f"❌ 爬取社交媒体数据失败: {e}")
            
        return social_data
        
    def scrape_weather_traffic_impact(self) -> List[Dict]:
        """爬取天气对交通的影响数据"""
        weather_impact_data = []
        
        try:
            # 使用免费天气API获取天气数据
            # 这里使用OpenWeatherMap的免费API（需要注册获取API key）
            
            # 模拟天气数据
            weather_conditions = [
                {'condition': 'rain', 'impact': 'high', 'description': '降雨影响道路通行'},
                {'condition': 'snow', 'impact': 'very_high', 'description': '降雪严重影响交通'},
                {'condition': 'fog', 'impact': 'medium', 'description': '大雾影响能见度'},
                {'condition': 'clear', 'impact': 'low', 'description': '天气良好'}
            ]
            
            current_weather = random.choice(weather_conditions)
            
            if current_weather['impact'] in ['high', 'very_high']:
                # 生成天气影响的交通事件
                for i in range(random.randint(1, 3)):
                    event_data = {
                        'source': 'weather_api',
                        'event_type': 'weather',
                        'event_level': 'warning' if current_weather['impact'] == 'high' else 'urgent',
                        'location_id': f'LOC{random.randint(1, 4):03d}',
                        'location_name': f'受天气影响路段{i+1}',
                        'description': current_weather['description'],
                        'start_time': datetime.utcnow(),
                        'status': 'active',
                        'weather_condition': current_weather['condition']
                    }
                    weather_impact_data.append(event_data)
                    
            logger.info(f"✅ 获取天气影响数据 {len(weather_impact_data)} 条")
            
        except Exception as e:
            logger.error(f"❌ 获取天气影响数据失败: {e}")
            
        return weather_impact_data
        
    def scrape_real_time_traffic_apis(self) -> List[Dict]:
        """爬取实时交通API数据"""
        api_data = []
        
        try:
            # 模拟调用多个交通API
            api_endpoints = [
                {
                    'name': '高德地图API',
                    'url': 'https://restapi.amap.com/v3/traffic/status/rectangle',
                    'enabled': False,  # 需要API key
                    'mock_data': self._generate_amap_mock_data
                },
                {
                    'name': '百度地图API',
                    'url': 'https://api.map.baidu.com/traffic/v1/road',
                    'enabled': False,  # 需要API key
                    'mock_data': self._generate_baidu_mock_data
                },
                {
                    'name': '腾讯地图API',
                    'url': 'https://apis.map.qq.com/ws/traffic/v1/roads',
                    'enabled': False,  # 需要API key
                    'mock_data': self._generate_tencent_mock_data
                }
            ]
            
            for api in api_endpoints:
                try:
                    if api['enabled']:
                        # 调用真实API
                        data = self._call_traffic_api(api['url'])
                    else:
                        # 使用模拟数据
                        data = api['mock_data']()
                        
                    if data:
                        api_data.extend(data)
                        logger.info(f"✅ 从 {api['name']} 获取 {len(data)} 条数据")
                        
                except Exception as e:
                    logger.error(f"❌ 调用 {api['name']} 失败: {e}")
                    
            return api_data
            
        except Exception as e:
            logger.error(f"❌ 爬取实时交通API数据失败: {e}")
            return []
            
    def _scrape_website(self, url: str, parser_func) -> List[Dict]:
        """爬取网站数据的通用方法"""
        try:
            response = self.session.get(url, timeout=CrawlerConfig.REQUEST_TIMEOUT)
            if response.status_code == 200:
                return parser_func(response.text, url)
            else:
                logger.warning(f"HTTP {response.status_code}: {url}")
                return []
        except Exception as e:
            logger.error(f"爬取网站失败 {url}: {e}")
            return []
            
    def _parse_traffic_data(self, html_content: str, url: str) -> Dict:
        """解析HTML内容中的交通数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找包含交通数据的元素
            traffic_elements = soup.find_all(['div', 'span', 'p'], 
                                           text=re.compile(r'交通|车流|拥堵|事故', re.I))
            
            parsed_data = {
                'url': url,
                'timestamp': datetime.utcnow(),
                'traffic_info': []
            }
            
            for element in traffic_elements:
                text = element.get_text().strip()
                if len(text) > 10:  # 过滤太短的文本
                    parsed_data['traffic_info'].append(text)
                    
            return parsed_data
            
        except Exception as e:
            logger.error(f"解析HTML内容失败: {e}")
            return {}
            
    def _parse_mot_data(self, html_content: str, url: str) -> List[Dict]:
        """解析交通运输部数据"""
        # 模拟解析交通运输部网站数据
        return self._generate_mock_government_data('交通运输部')
        
    def _parse_beijing_traffic_data(self, html_content: str, url: str) -> List[Dict]:
        """解析北京交通委数据"""
        return self._generate_mock_government_data('北京交通委')
        
    def _parse_shanghai_traffic_data(self, html_content: str, url: str) -> List[Dict]:
        """解析上海交通委数据"""
        return self._generate_mock_government_data('上海交通委')
        
    def _generate_mock_government_data(self, source: str) -> List[Dict]:
        """生成模拟政府数据"""
        mock_data = []
        
        # 生成1-3条模拟数据
        for i in range(random.randint(1, 3)):
            data = {
                'source': source,
                'data_type': 'government_report',
                'location_id': f'LOC{random.randint(1, 4):03d}',
                'location_name': f'{source}监测点{i+1}',
                'flow_count': random.randint(100, 300),
                'record_time': datetime.utcnow(),
                'vehicle_type': 'all',
                'direction': 'all',
                'reliability': 'high'
            }
            mock_data.append(data)
            
        return mock_data
        
    def _scrape_traffic_news(self, url: str, keywords: List[str]) -> List[Dict]:
        """爬取交通新闻"""
        news_events = []
        
        try:
            # 模拟新闻爬取
            for i in range(random.randint(0, 2)):
                event = {
                    'source': 'news_website',
                    'event_type': 'news_report',
                    'event_level': random.choice(['normal', 'warning']),
                    'location_id': f'LOC{random.randint(1, 4):03d}',
                    'location_name': f'新闻报道地点{i+1}',
                    'description': f'新闻报道: {random.choice(keywords)}相关事件',
                    'start_time': datetime.utcnow() - timedelta(hours=random.randint(1, 12)),
                    'status': 'reported',
                    'news_url': url
                }
                news_events.append(event)
                
        except Exception as e:
            logger.error(f"爬取交通新闻失败: {e}")
            
        return news_events
        
    def _generate_amap_mock_data(self) -> List[Dict]:
        """生成高德地图模拟数据"""
        return [
            {
                'source': 'amap_api',
                'location_id': f'LOC{i:03d}',
                'location_name': f'高德监测点{i}',
                'flow_count': random.randint(80, 200),
                'average_speed': random.randint(30, 80),
                'congestion_level': random.randint(1, 4),
                'record_time': datetime.utcnow()
            }
            for i in range(1, 5)
        ]
        
    def _generate_baidu_mock_data(self) -> List[Dict]:
        """生成百度地图模拟数据"""
        return [
            {
                'source': 'baidu_api',
                'location_id': f'LOC{i:03d}',
                'location_name': f'百度监测点{i}',
                'flow_count': random.randint(70, 180),
                'traffic_status': random.choice(['畅通', '缓慢', '拥堵']),
                'record_time': datetime.utcnow()
            }
            for i in range(1, 5)
        ]
        
    def _generate_tencent_mock_data(self) -> List[Dict]:
        """生成腾讯地图模拟数据"""
        return [
            {
                'source': 'tencent_api',
                'location_id': f'LOC{i:03d}',
                'location_name': f'腾讯监测点{i}',
                'flow_count': random.randint(90, 220),
                'road_condition': random.choice(['good', 'fair', 'poor']),
                'record_time': datetime.utcnow()
            }
            for i in range(1, 5)
        ]
        
    def _call_traffic_api(self, url: str) -> List[Dict]:
        """调用真实的交通API"""
        try:
            response = self.session.get(url, timeout=CrawlerConfig.REQUEST_TIMEOUT)
            if response.status_code == 200:
                data = response.json()
                # 根据API返回格式解析数据
                return self._parse_api_response(data)
            else:
                logger.warning(f"API调用失败 {response.status_code}: {url}")
                return []
        except Exception as e:
            logger.error(f"调用API失败 {url}: {e}")
            return []
            
    def _parse_api_response(self, api_data: Dict) -> List[Dict]:
        """解析API响应数据"""
        parsed_data = []
        
        try:
            # 根据不同API的数据格式进行解析
            # 这里需要根据实际API的返回格式来实现
            
            if isinstance(api_data, dict):
                # 处理字典格式的API响应
                if 'data' in api_data:
                    for item in api_data['data']:
                        parsed_item = self._normalize_api_data(item)
                        if parsed_item:
                            parsed_data.append(parsed_item)
                            
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
            
        return parsed_data
        
    def _normalize_api_data(self, raw_data: Dict) -> Dict:
        """标准化API数据格式"""
        try:
            # 将不同API的数据格式标准化
            normalized = {
                'source': 'external_api',
                'location_id': raw_data.get('location_id', 'UNKNOWN'),
                'location_name': raw_data.get('location_name', '未知位置'),
                'record_time': datetime.utcnow(),
                'raw_data': raw_data
            }
            
            # 根据数据类型添加特定字段
            if 'flow_count' in raw_data:
                normalized['flow_count'] = raw_data['flow_count']
                normalized['data_type'] = 'traffic_flow'
            elif 'speed' in raw_data:
                normalized['speed'] = raw_data['speed']
                normalized['data_type'] = 'vehicle_speed'
                
            return normalized
            
        except Exception as e:
            logger.error(f"标准化API数据失败: {e}")
            return {}

# 创建全局网络爬虫实例
web_scraper = WebScraper()
