# -*- coding: utf-8 -*-
"""
数据爬取配置文件
"""

import os

class CrawlerConfig:
    """爬虫配置类"""
    
    # 请求头配置
    DEFAULT_HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache'
    }
    
    # 请求配置
    REQUEST_TIMEOUT = 30
    REQUEST_RETRY = 3
    REQUEST_DELAY = 1  # 请求间隔（秒）
    
    # 数据源配置
    DATA_SOURCES = {
        # 高德地图交通态势API
        'amap_traffic': {
            'name': '高德地图交通态势',
            'base_url': 'https://restapi.amap.com/v3/traffic/status/rectangle',
            'key': os.environ.get('AMAP_API_KEY', ''),  # 需要申请高德API密钥
            'enabled': False,  # 需要API密钥才能启用
            'params': {
                'rectangle': '116.351147,39.966309;116.357134,39.968727',  # 北京某区域
                'extensions': 'all',
                'level': 9
            }
        },
        
        # 百度地图交通流量API
        'baidu_traffic': {
            'name': '百度地图交通流量',
            'base_url': 'https://api.map.baidu.com/traffic/v1/road',
            'key': os.environ.get('BAIDU_API_KEY', ''),  # 需要申请百度API密钥
            'enabled': False,  # 需要API密钥才能启用
            'params': {
                'coord_type_input': 'bd09ll',
                'coord_type_output': 'bd09ll'
            }
        },
        
        # 模拟交通数据API（用于演示）
        'mock_traffic_api': {
            'name': '模拟交通数据API',
            'base_url': 'https://jsonplaceholder.typicode.com/posts',  # 使用公开API模拟
            'enabled': True,
            'transform_function': 'transform_mock_data'
        },
        
        # 公开交通数据源
        'open_traffic_data': {
            'name': '公开交通数据',
            'sources': [
                {
                    'name': '北京交通委员会',
                    'url': 'http://www.bjjtw.gov.cn/',
                    'type': 'html_parse',
                    'enabled': False
                },
                {
                    'name': '上海交通委员会',
                    'url': 'http://jtw.sh.gov.cn/',
                    'type': 'html_parse',
                    'enabled': False
                }
            ]
        }
    }
    
    # 爬取任务配置
    CRAWL_TASKS = {
        'traffic_flow': {
            'name': '车流量数据爬取',
            'interval': 300,  # 5分钟
            'enabled': True,
            'sources': ['mock_traffic_api']
        },
        'traffic_events': {
            'name': '交通事件爬取',
            'interval': 600,  # 10分钟
            'enabled': True,
            'sources': ['mock_traffic_api']
        },
        'device_status': {
            'name': '设备状态爬取',
            'interval': 180,  # 3分钟
            'enabled': True,
            'sources': ['mock_traffic_api']
        },
        'violation_records': {
            'name': '违法记录爬取',
            'interval': 900,  # 15分钟
            'enabled': True,
            'sources': ['mock_traffic_api']
        }
    }
    
    # 数据存储配置
    STORAGE_CONFIG = {
        'batch_size': 100,  # 批量插入大小
        'max_retries': 3,   # 最大重试次数
        'cleanup_days': 30, # 数据保留天数
        'backup_enabled': True
    }
    
    # 日志配置
    LOG_CONFIG = {
        'level': 'INFO',
        'file': 'logs/crawler.log',
        'max_size': '10MB',
        'backup_count': 5
    }
    
    # 监控配置
    MONITOR_CONFIG = {
        'health_check_interval': 60,  # 健康检查间隔（秒）
        'alert_threshold': {
            'error_rate': 0.1,  # 错误率阈值
            'response_time': 10  # 响应时间阈值（秒）
        }
    }

# 地理位置配置
LOCATION_CONFIG = {
    'default_locations': [
        {
            'id': 'LOC001',
            'name': 'G15沈海高速K1234+500',
            'coordinates': {'lat': 31.2304, 'lng': 121.4737},  # 上海
            'type': 'highway'
        },
        {
            'id': 'LOC002', 
            'name': 'G15沈海高速K1235+200',
            'coordinates': {'lat': 31.2404, 'lng': 121.4837},
            'type': 'highway'
        },
        {
            'id': 'LOC003',
            'name': '主要路口01',
            'coordinates': {'lat': 31.2204, 'lng': 121.4637},
            'type': 'intersection'
        },
        {
            'id': 'LOC004',
            'name': '主要路口02', 
            'coordinates': {'lat': 31.2504, 'lng': 121.4937},
            'type': 'intersection'
        }
    ]
}

# 数据转换规则
DATA_TRANSFORM_RULES = {
    'traffic_flow': {
        'required_fields': ['location_id', 'flow_count', 'record_time'],
        'field_mapping': {
            'location': 'location_id',
            'count': 'flow_count',
            'time': 'record_time',
            'type': 'vehicle_type'
        },
        'default_values': {
            'vehicle_type': 'all',
            'direction': 'unknown'
        }
    },
    'vehicle_speed': {
        'required_fields': ['location_id', 'speed', 'record_time'],
        'field_mapping': {
            'location': 'location_id',
            'velocity': 'speed',
            'time': 'record_time'
        },
        'default_values': {
            'speed_limit': 60.0,
            'is_violation': False
        }
    },
    'traffic_event': {
        'required_fields': ['event_type', 'location_id', 'start_time'],
        'field_mapping': {
            'type': 'event_type',
            'location': 'location_id',
            'time': 'start_time',
            'desc': 'description'
        },
        'default_values': {
            'event_level': 'normal',
            'status': 'active'
        }
    }
}
