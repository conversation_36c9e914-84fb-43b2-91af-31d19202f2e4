#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器启动脚本
"""

import os
import sys
import logging
from flask import Flask

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, crawler
from init_db import create_database, init_sample_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_database():
    """设置数据库"""
    try:
        logger.info("🔧 开始设置数据库...")
        
        # 创建数据库
        if create_database():
            logger.info("✅ 数据库创建成功")
        else:
            logger.warning("⚠️ 数据库创建失败，可能已存在")
        
        # 创建表结构
        with app.app_context():
            db.create_all()
            logger.info("✅ 数据表创建成功")
            
            # 检查是否需要初始化示例数据
            from models import TrafficFlow
            if TrafficFlow.query.count() == 0:
                logger.info("📊 初始化示例数据...")
                init_sample_data()
                logger.info("✅ 示例数据初始化完成")
            else:
                logger.info("📊 数据库已有数据，跳过初始化")
                
    except Exception as e:
        logger.error(f"❌ 数据库设置失败: {e}")
        return False
        
    return True

def start_crawler_service():
    """启动爬虫服务"""
    try:
        logger.info("🕷️ 启动数据爬虫服务...")
        crawler.start_crawling()
        logger.info("✅ 数据爬虫服务启动成功")
    except Exception as e:
        logger.error(f"❌ 启动爬虫服务失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 智慧城市交通流量可视化数据分析系统")
    print("=" * 60)
    
    # 设置数据库
    if not setup_database():
        logger.error("❌ 数据库设置失败，退出程序")
        sys.exit(1)
    
    # 启动爬虫服务
    start_crawler_service()
    
    # 打印服务信息
    print("\n📡 API服务信息:")
    print(f"   - 服务地址: http://localhost:5000")
    print(f"   - API文档: http://localhost:5000")
    print(f"   - 车流量数据: http://localhost:5000/api/traffic/flow")
    print(f"   - 实时数据: http://localhost:5000/api/traffic/realtime")
    print(f"   - 爬虫状态: http://localhost:5000/api/crawler/status")
    print(f"   - 数据库统计: http://localhost:5000/api/database/stats")
    
    print("\n🕷️ 数据爬虫功能:")
    print(f"   - 启动爬虫: POST http://localhost:5000/api/crawler/start")
    print(f"   - 停止爬虫: POST http://localhost:5000/api/crawler/stop")
    print(f"   - 网络爬取: POST http://localhost:5000/api/scraper/run")
    
    print("\n💾 数据库配置:")
    print(f"   - 主机: {app.config.get('MYSQL_HOST', 'localhost')}")
    print(f"   - 端口: {app.config.get('MYSQL_PORT', 3306)}")
    print(f"   - 数据库: {app.config.get('MYSQL_DATABASE', 'smart_traffic')}")
    print(f"   - 用户: {app.config.get('MYSQL_USER', 'root')}")
    
    print("\n" + "=" * 60)
    print("🎯 服务已启动，按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 启动Flask应用
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            threaded=True
        )
    except KeyboardInterrupt:
        logger.info("🛑 收到停止信号，正在关闭服务...")
        crawler.stop_crawling()
        logger.info("✅ 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务运行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
